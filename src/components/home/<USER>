import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import profileService from '../../services/profileService';
import { getCdnUrl } from '../../utils/cdnUtils';
import useTranslation from '../../hooks/useTranslation';

const AuthenticatedUserWelcome = () => {
    const { isAuthenticated, user } = useAuth();
    const { t } = useTranslation(['home', 'common']);
    const [profileData, setProfileData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Availability status constants
    const AVAILABILITY_RED = '#FF0000';
    const AVAILABILITY_YELLOW = '#FFFF00';
    const AVAILABILITY_GREEN = '#00FF00';

    useEffect(() => {
        const fetchProfileData = async () => {
            if (!isAuthenticated) {
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                setError(null);
                
                const response = await profileService.getCompleteProfile();
                
                if (response.success) {
                    setProfileData(response.data);
                } else {
                    setError(response.error || 'Failed to load profile data');
                }
            } catch (err) {
                console.error('Error fetching profile data:', err);
                setError('Failed to load profile data');
            } finally {
                setLoading(false);
            }
        };

        fetchProfileData();
    }, [isAuthenticated]);

    // Don't render anything if user is not authenticated
    if (!isAuthenticated) {
        return null;
    }

    // Show nothing if the user's role is customer
    if (!loading && profileData?.role === 'customer') {
        return null;
    }

    // Show loading state with enhanced skeleton
    if (loading) {
        return (
            <motion.div 
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 rounded-3xl p-6 shadow-xl border border-blue-100/50 dark:border-gray-700/50"
            >
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <div className="relative">
                            <div className="w-14 h-14 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 rounded-full animate-pulse"></div>
                            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded-full animate-pulse"></div>
                        </div>
                        <div className="flex-1">
                            <div className="h-5 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 rounded-lg w-40 mb-2 animate-pulse"></div>
                            <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 rounded-lg w-56 animate-pulse"></div>
                        </div>
                    </div>
                    <div className="w-20 h-7 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 rounded-full animate-pulse"></div>
                </div>
            </motion.div>
        );
    }

    // Show enhanced error state
    if (error) {
        return (
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 bg-gradient-to-br from-red-50 via-white to-red-50 dark:from-red-900/20 dark:via-gray-800 dark:to-red-900/20 border border-red-200 dark:border-red-800/50 rounded-3xl p-6 shadow-xl"
            >
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <motion.div 
                            className="w-14 h-14 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-800 dark:to-red-900 rounded-full flex items-center justify-center shadow-lg"
                            whileHover={{ scale: 1.05 }}
                            transition={{ duration: 0.2 }}
                        >
                            <svg className="w-7 h-7 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </motion.div>
                        <div className="flex-1">
                            <p className="text-red-800 dark:text-red-200 font-bold text-lg">Unable to load profile</p>
                            <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
                        </div>
                    </div>
                    <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => window.location.reload()}
                        className="px-4 py-2 bg-red-100 hover:bg-red-200 dark:bg-red-800 dark:hover:bg-red-700 text-red-700 dark:text-red-200 rounded-full text-sm font-medium transition-colors duration-200"
                    >
                        Retry
                    </motion.button>
                </div>
            </motion.div>
        );
    }

    // Extract data from profile response
    const displayName = profileData?.nickname || user?.name || 'User';
    const profilePicture = profileData?.profile_picture;
    const availabilityStatus = profileData?.availability_status;

    // Get profile image URL
    const getProfileImageUrl = () => {
        if (profilePicture) {
            return getCdnUrl(profilePicture);
        }
        return '/images/profile-placeholder.svg'; // Fallback avatar
    };

    // Get availability status information based on hex codes
    const getAvailabilityInfo = () => {
        if (!availabilityStatus) {
            return {
                color: '#6B7280', // gray-500
                text: 'Offline',
                description: 'You appear offline',
                bgColor: 'bg-gray-100 dark:bg-gray-700/50',
                textColor: 'text-gray-700 dark:text-gray-300',
                gradientFrom: 'from-gray-50',
                gradientTo: 'to-gray-100',
                darkGradientFrom: 'dark:from-gray-800',
                darkGradientTo: 'dark:to-gray-700',
                icon: (
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                )
            };
        }

        switch (availabilityStatus.toUpperCase()) {
            case AVAILABILITY_RED:
                return {
                    color: '#EF4444', // Enhanced red for better visibility
                    text: 'Offline',
                    description: 'Currently offline, Please update your user availability in Profile, My Availability',
                    bgColor: 'bg-red-100 dark:bg-red-900/30',
                    textColor: 'text-red-700 dark:text-red-300',
                    gradientFrom: 'from-red-50',
                    gradientTo: 'to-red-100',
                    darkGradientFrom: 'dark:from-red-900/20',
                    darkGradientTo: 'dark:to-red-800/20',
                    icon: (
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                    )
                };
            case AVAILABILITY_YELLOW:
                return {
                    color: '#F59E0B', // Enhanced yellow/amber for better visibility
                    text: 'Busy',
                    description: 'In game or busy',
                    bgColor: 'bg-amber-100 dark:bg-amber-900/30',
                    textColor: 'text-amber-700 dark:text-amber-300',
                    gradientFrom: 'from-amber-50',
                    gradientTo: 'to-yellow-100',
                    darkGradientFrom: 'dark:from-amber-900/20',
                    darkGradientTo: 'dark:to-yellow-800/20',
                    icon: (
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                    )
                };
            case AVAILABILITY_GREEN:
                return {
                    color: '#10B981', // Enhanced green for better visibility
                    text: 'Online',
                    description: 'Ready for missions',
                    bgColor: 'bg-emerald-100 dark:bg-emerald-900/30',
                    textColor: 'text-emerald-700 dark:text-emerald-300',
                    gradientFrom: 'from-emerald-50',
                    gradientTo: 'to-green-100',
                    darkGradientFrom: 'dark:from-emerald-900/20',
                    darkGradientTo: 'dark:to-green-800/20',
                    icon: (
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                    )
                };
            default:
                // If it's a hex color but not one of our predefined ones, treat as online
                return {
                    color: availabilityStatus,
                    text: 'Online',
                    description: 'Active',
                    bgColor: 'bg-blue-100 dark:bg-blue-900/30',
                    textColor: 'text-blue-700 dark:text-blue-300',
                    gradientFrom: 'from-blue-50',
                    gradientTo: 'to-blue-100',
                    darkGradientFrom: 'dark:from-blue-900/20',
                    darkGradientTo: 'dark:to-blue-800/20',
                    icon: (
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                    )
                };
        }
    };

    const availabilityInfo = getAvailabilityInfo();

    return (
        <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className={`mb-6 bg-gradient-to-br ${availabilityInfo.gradientFrom} via-white ${availabilityInfo.gradientTo} ${availabilityInfo.darkGradientFrom} dark:via-gray-800 ${availabilityInfo.darkGradientTo} rounded-3xl p-6 shadow-xl border border-slate-200/50 dark:border-gray-700/50 backdrop-blur-sm`}
        >
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-5">
                    {/* Enhanced Profile Picture with Status Indicator */}
                    <div className="relative group">
                        <motion.div
                            className="relative"
                            whileHover={{ scale: 1.05 }}
                            transition={{ duration: 0.2 }}
                        >
                            <img
                                src={getProfileImageUrl()}
                                alt={`${displayName}'s profile`}
                                className="w-14 h-14 rounded-full object-cover border-3 border-white dark:border-gray-700 shadow-lg ring-2 ring-slate-200/50 dark:ring-gray-600/50"
                                onError={(e) => {
                                    e.target.src = '/images/profile-placeholder.svg';
                                }}
                            />
                            {/* Enhanced Availability Status Indicator with Animation */}
                            <motion.div
                                className="absolute -bottom-1 -right-1 w-5 h-5 rounded-full text-left border-2 border-white dark:border-gray-800 shadow-lg"
                                style={{ backgroundColor: availabilityInfo.color }}
                                title={availabilityInfo.description}
                                animate={{ 
                                    scale: availabilityInfo.text === 'Online' ? [1, 1.1, 1] : 1,
                                }}
                                transition={{ 
                                    duration: 2, 
                                    repeat: availabilityInfo.text === 'Online' ? Infinity : 0,
                                    ease: "easeInOut" 
                                }}
                            >
                                <div className="w-full h-full rounded-full flex items-center justify-center">
                                    <div className="text-white text-xs">
                                        {availabilityInfo.icon}
                                    </div>
                                </div>
                            </motion.div>
                        </motion.div>
                        
                        {/* Hover effect ring */}
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400/0 via-purple-400/0 to-pink-400/0 group-hover:from-blue-400/20 group-hover:via-purple-400/20 group-hover:to-pink-400/20 transition-all duration-300 -z-10 scale-125"></div>
                    </div>

                    {/* Enhanced Welcome Text */}
                    <div className="flex-1">
                        <motion.h2
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.2, duration: 0.5 }}
                            className="text-2xl text-left font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-gray-900 dark:from-white dark:via-blue-100 dark:to-white bg-clip-text text-transparent"
                        >
                            Welcome back, {displayName}!
                        </motion.h2>
                        <motion.p
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.3, duration: 0.5 }}
                            className="text-sm text-gray-600 dark:text-gray-300 text-left mt-1"
                        >
                            {availabilityInfo.text === 'Online' 
                                ? "Ready to embark on your next mission?" 
                                : availabilityInfo.text === 'Busy' 
                                ? "We see you're busy - take your time!" 
                                : "You're currently offline, Please update your user availability in Profile, My Availability to be Online."
                            }
                        </motion.p>
                    </div>
                </div>

                {/* Enhanced Status Badge */}
                <motion.div
                    initial={{ opacity: 0, scale: 0.8, x: 20 }}
                    animate={{ opacity: 1, scale: 1, x: 0 }}
                    transition={{ delay: 0.4, duration: 0.5 }}
                    whileHover={{ scale: 1.05 }}
                    className={`px-4 py-2 rounded-full text-sm font-semibold ${availabilityInfo.bgColor} ${availabilityInfo.textColor} flex items-center space-x-2 shadow-lg border border-white/20 dark:border-gray-700/20 backdrop-blur-sm`}
                >
                    <motion.div
                        className="w-2.5 h-2.5 rounded-full shadow-sm"
                        style={{ backgroundColor: availabilityInfo.color }}
                        animate={{ 
                            opacity: availabilityInfo.text === 'Online' ? [1, 0.5, 1] : 1,
                        }}
                        transition={{ 
                            duration: 2, 
                            repeat: availabilityInfo.text === 'Online' ? Infinity : 0,
                            ease: "easeInOut" 
                        }}
                    />
                    <span className="font-medium">{availabilityInfo.text}</span>
                </motion.div>
            </div>

            {/* Optional: Add a subtle animated background pattern */}
            <div className="absolute inset-0 rounded-3xl opacity-5 dark:opacity-10 overflow-hidden pointer-events-none">
                <motion.div
                    className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full blur-xl"
                    animate={{ 
                        scale: [1, 1.2, 1],
                        opacity: [0.1, 0.2, 0.1]
                    }}
                    transition={{ 
                        duration: 4, 
                        repeat: Infinity, 
                        ease: "easeInOut" 
                    }}
                />
                <motion.div
                    className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-tr from-pink-400 to-blue-500 rounded-full blur-xl"
                    animate={{ 
                        scale: [1.2, 1, 1.2],
                        opacity: [0.1, 0.2, 0.1]
                    }}
                    transition={{ 
                        duration: 6, 
                        repeat: Infinity, 
                        ease: "easeInOut",
                        delay: 2
                    }}
                />
            </div>
        </motion.div>
    );
};

export default AuthenticatedUserWelcome;