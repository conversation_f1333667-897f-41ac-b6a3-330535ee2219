import React, { useState, useEffect, useRef, useCallback, memo, useLayoutEffect } from 'react';
import { chatApi } from '../../services/chatApi';
import { getCdnUrl } from '../../utils/cdnUtils';
import { DateTime } from 'luxon';
import { Textarea } from "../ui/textarea";
import { Button } from "../ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "../ui/avatar";
import { cn } from "../../lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowLeftIcon } from '@radix-ui/react-icons';
import { useVirtualizer } from '@tanstack/react-virtual';
import GiftModal from './GiftModal';
import GiftNotification from './GiftNotification';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../contexts/AuthContext';
import { useNetwork } from '../../hooks/useNetwork';
import { SectionLoader } from '../../components/ui/LoadingIndicator';
import { useWalletQuery } from '../../features/wallet/hooks/useWalletQuery';
import { useNotifications } from '../../context/NotificationContext';
import GiftMessageBubble from './GiftMessageBubble';
import { useLocation } from 'react-router-dom';

// Helper to get user info from chat object
const getUserInfo = (chat, currentUserId) => {
    // Handle the backend data structure: chat_partner (primary structure)
    if (chat.chat_partner && chat.chat_partner.id !== currentUserId) {
        return {
            id: chat.chat_partner.id,
            uid: chat.chat_partner.uid,
            nickname: chat.chat_partner.nickname,
            profile_image: chat.chat_partner.profile_picture,
            name: chat.chat_partner.name,
            is_online: chat.chat_partner.is_online,
            last_seen_at: chat.chat_partner.last_active_at || chat.chat_partner.last_seen_at
        };
    }

    // Handle the backend data structure: participants.user (secondary structure)
    if (chat.participants && Array.isArray(chat.participants)) {
        // Find the participant that is not the current user
        const otherParticipant = chat.participants.find(participant =>
            participant.user && participant.user.id !== currentUserId
        );

        if (otherParticipant && otherParticipant.user) {
            return {
                id: otherParticipant.user.id,
                uid: otherParticipant.user.uid,
                nickname: otherParticipant.user.nickname,
                profile_image: otherParticipant.user.profile_picture,
                name: otherParticipant.user.name,
                is_online: otherParticipant.user.is_online,
                last_seen_at: otherParticipant.user.last_active_at || otherParticipant.user.last_seen_at
            };
        }
    }

    // Fallback to old structure (user/talent) for backward compatibility
    if (chat.user && chat.user.id !== currentUserId) {
        return {
            id: chat.user.id,
            uid: chat.user.uid,
            nickname: chat.user.nickname,
            profile_image: chat.user.profile_picture,
            name: chat.user.name,
            is_online: chat.user.is_online,
            last_seen_at: chat.user.last_active_at || chat.user.last_seen_at
        };
    }

    if (chat.talent && chat.talent.id !== currentUserId) {
        return {
            id: chat.talent.id,
            uid: chat.talent.uid,
            nickname: chat.talent.nickname || chat.talent.displayName,
            profile_image: chat.talent.profile_picture || chat.talent.profileImage,
            name: chat.talent.name || chat.talent.displayName,
            is_online: chat.talent.is_online ?? chat.talent.isOnline,
            last_seen_at: chat.talent.last_active_at || chat.talent.last_seen_at
        };
    }

    return null;
};

// Quick reactions
const QUICK_REACTIONS = ['👍', '❤️', '😂', '😮', '😢', '🙏'];

// Add ModeratedContentNotice component
const ModeratedContentNotice = ({ reason, severity = 'warning' }) => {
    const severityStyles = {
        warning: "bg-amber-50 text-amber-500 border-amber-200",
        error: "bg-red-50 text-red-500 border-red-200",
        info: "bg-blue-50 text-blue-500 border-blue-200"
    };

    return (
        <div className={`flex items-center text-xs mb-1 px-2 py-1 rounded-lg border ${severityStyles[severity]}`}>
            <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span>
                {reason || 'This content has been flagged and is under review'}
            </span>
        </div>
    );
};

const formatLastSeen = (timestamp) => {
    if (!timestamp) return 'Offline';
    const lastSeen = DateTime.fromISO(timestamp);
    const now = DateTime.now();
    const diff = now.diff(lastSeen, ['minutes', 'hours', 'days']).toObject();

    if (diff.minutes < 1) return 'Just now';
    if (diff.minutes < 60) return `${Math.floor(diff.minutes)}m ago`;
    if (diff.hours < 24) return `${Math.floor(diff.hours)}h ago`;
    if (diff.days < 7) return `${Math.floor(diff.days)}d ago`;
    return lastSeen.toFormat('MMM d, yyyy');
};

// Format date for message groups
const formatMessageDate = (dateStr) => {
    try {
        const date = DateTime.fromISO(dateStr);
        const now = DateTime.now();
        const yesterday = now.minus({ days: 1 });

        if (date.hasSame(now, 'day')) return 'Today';
        if (date.hasSame(yesterday, 'day')) return 'Yesterday';
        if (date.hasSame(now, 'week')) return date.toFormat('cccc'); // Day name
        if (date.hasSame(now, 'year')) return date.toFormat('LLL d'); // Month day
        return date.toFormat('LLL d, yyyy'); // Month day, year
    } catch (error) {
        console.error('Date parsing error:', error);
        return 'Unknown date';
    }
};

// Format time for message bubbles
const formatMessageTime = (dateStr) => {
    try {
        const date = DateTime.fromISO(dateStr);
        return date.toFormat('t');
    } catch (error) {
        console.error('Date parsing error:', error);
        return '';
    }
};

// Utility to merge, infer type, and sort messages by id and created_at
function inferMessageType(msg) {
    // Check for gift messages first
    if (msg.gift_data || (msg.gifts && msg.gifts.length > 0)) return 'gift';
    
    // Check for attachments and determine if they're images
    if (msg.attachments && msg.attachments.length > 0) {
        // Check if all attachments are images
        const allImages = msg.attachments.every(att => {
            // Check mime type first
            if (att.mime_type && att.mime_type.startsWith('image/')) return true;
            
            // Check filename extension as fallback
            if (att.filename && att.filename.match(/\.(jpg|jpeg|png|gif|webp|heic|heif|bmp|svg)$/i)) return true;
            
            // Check if it has image-related properties
            if (att.optimized_path && att.optimized_path.includes('image')) return true;
            
            return false;
        });
        
        return allImages ? 'image' : 'file';
    }
    
    // System messages
    if (msg.is_system || msg.type === 'system') return 'system';
    
    // Default to text
    return 'text';
}

function attachmentsEqual(a1 = [], a2 = []) {
    if (a1.length !== a2.length) return false;
    for (let i = 0; i < a1.length; i++) {
        if (
            a1[i].id !== a2[i].id ||
            getAttachmentUrl(a1[i]) !== getAttachmentUrl(a2[i])
        ) {
            return false;
        }
    }
    return true;
}

function mergeAndSortMessages(existing, incoming) {
    const map = new Map(existing.map(msg => [msg.id, msg]));
    let changed = false;

    incoming.forEach(msg => {
        const normalized = { ...msg, type: inferMessageType(msg) };
        const prev = map.get(msg.id);

        if (!prev) {
            map.set(msg.id, normalized);
            changed = true;
            return;
        }

        const contentChanged = prev.content !== normalized.content;
        const attachmentsChanged = !attachmentsEqual(prev.attachments, normalized.attachments);
        const typeChanged = prev.type !== normalized.type;

        if (contentChanged || attachmentsChanged || typeChanged) {
            map.set(msg.id, normalized);
            changed = true;
        }
    });

    if (!changed && existing.length === map.size) return existing;
    return Array.from(map.values()).sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
}

// Normalize a single message object by adding a computed `type` field
function normalizeMessage(msg) {
    const normalized = { ...msg, type: inferMessageType(msg) };

    // Some API responses return gifts as an array under `gifts`. Convert the
    // first entry into a gift_data object so GiftMessageBubble can display it.
    if (!normalized.gift_data && Array.isArray(normalized.gifts) && normalized.gifts.length > 0) {
        const g = normalized.gifts[0];
        normalized.gift_data = {
            id: g.gift_item_id,
            quantity: g.quantity,
            name: g.gift_item?.name,
            icon_path: g.gift_item?.icon_path
        };
    }

    if (normalized.gift_data && normalized.gift_data.icon_path) {
        normalized.gift_data.icon_path = getCdnUrl(normalized.gift_data.icon_path);
    }
    if (normalized.gift && normalized.gift.icon_path) {
        normalized.gift.icon_path = getCdnUrl(normalized.gift.icon_path);
    }
    return normalized;
}

// Derive an attachment URL regardless of backend key naming
function getAttachmentUrl(att = {}) {
    // Prioritize optimized path for images
    if (att.optimized_path) return att.optimized_path;
    if (att.path) return att.path;
    if (att.file_path) return att.file_path;
    if (att.url) return att.url;
    return '';
}

// MessageItem component - renders individual messages
const MessageItem = memo(
    ({ msg, chat, rowVirtualizer, index }) => {
        const { user } = useAuth();
        // Compare as strings to avoid type mismatch
        const isCurrentUser = String(msg.sender_id) === String(user?.id);
        // Get partner info for avatar and display name
        const partnerInfo = getUserInfo(chat, user?.id);
        // Remove or gate debug logs for production
        // console.debug('[ChatWindow] MessageItem:', { msg, isCurrentUser });
        const [isHighlighted, setIsHighlighted] = useState(false);
        const [showContextMenu, setShowContextMenu] = useState(false);
        const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
        const [selectedImage, setSelectedImage] = useState(null);
        const [isImageModalOpen, setIsImageModalOpen] = useState(false);
        const contextMenuRef = useRef(null);
        // Add this at the top of MessageItem
        const [imgLoaded, setImgLoaded] = useState({});
        // Ref for the message container
        const messageRef = useRef(null);

        // Get the last status from the message statuses array
        const getMessageStatus = () => {
            if (!msg.statuses || msg.statuses.length === 0) {
                return 'sent';
            }

            // Sort by timestamp to get the latest status
            const sortedStatuses = [...msg.statuses].sort((a, b) =>
                new Date(b.status_timestamp) - new Date(a.status_timestamp)
            );

            // Return the status of the last update
            return sortedStatuses[0].status;
        };

        // Render message status icon
        const renderStatusIcon = () => {
            const status = getMessageStatus();

            switch (status) {
                case 'sent':
                    return <svg className="w-4 h-4 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                    </svg>;
                case 'delivered':
                    return <svg className="w-4 h-4 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                        <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                    </svg>;
                case 'read':
                    return <svg className="w-4 h-4 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                        <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                    </svg>;
                case 'failed':
                    return <svg className="w-4 h-4 text-red-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>;
                default:
                    return null;
            }
        };

        // Handle context menu
        const handleContextMenu = (e) => {
            e.preventDefault();
            const rect = e.currentTarget.getBoundingClientRect();
            setContextMenuPosition({
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
            });
            setShowContextMenu(true);
        };

        // Handle image click
        const handleImageClick = (image) => {
            setSelectedImage(image);
            setIsImageModalOpen(true);
        };

        // Close context menu when clicking outside
        useEffect(() => {
            const handleClickOutside = (e) => {
                if (contextMenuRef.current && !contextMenuRef.current.contains(e.target)) {
                    setShowContextMenu(false);
                }
            };

            document.addEventListener('mousedown', handleClickOutside);
            return () => document.removeEventListener('mousedown', handleClickOutside);
        }, []);

        // Render message content based on type
        const renderMessageContent = useCallback(() => {
            // Debug log to help identify type inference issues
            if (process.env.NODE_ENV !== 'production' && (msg.type === 'default' || !msg.type)) {
                console.warn('Message type inference failed:', {
                    msgId: msg.id,
                    attachments: msg.attachments,
                    content: msg.content,
                    inferredType: inferMessageType(msg)
                });
            }

            switch (msg.type) {
                case 'text':
                    return (
                        <div
                            className={cn(
                                'whitespace-pre-wrap break-words',
                                isCurrentUser
                                    ? 'text-white text-right'
                                    : 'text-black dark:text-white text-left'
                            )}
                        >
                            {msg.content}
                        </div>
                    );
                case 'image':
                    return (
                        <div className="space-y-2">
                            {msg.content && (
                                <div
                                    className={cn(
                                        'whitespace-pre-wrap break-words',
                                        isCurrentUser
                                            ? 'text-white text-right'
                                            : 'text-black dark:text-white text-left'
                                    )}
                                >
                                    {msg.content}
                                </div>
                            )}
                            {msg.attachments && msg.attachments.length > 0 && (
                                <div className={cn(
                                    // Responsive grid: 1 column on mobile, 2+ on larger screens
                                    'grid gap-1',
                                    msg.attachments.length === 1 ? 'grid-cols-1' :
                                        msg.attachments.length === 2 ? 'grid-cols-2' :
                                            msg.attachments.length === 3 ? 'sm:grid-cols-2 md:grid-cols-3' :
                                                msg.attachments.length === 4 ? 'sm:grid-cols-2 md:grid-cols-2' :
                                                    'sm:grid-cols-2 md:grid-cols-3'
                                )}>
                                    {msg.attachments.map((attachment, index) => (
                                        <div
                                            key={attachment.id}
                                            className={cn(
                                                'relative aspect-square overflow-hidden rounded-lg cursor-pointer',
                                                'w-full max-w-xs max-h-60',
                                                'bg-gray-100 dark:bg-gray-800'
                                            )}
                                            onClick={() => {
                                                setSelectedImage(attachment);
                                                setIsImageModalOpen(true);
                                            }}
                                        >
                                            {/* Placeholder while loading */}
                                            {!imgLoaded[attachment.id] && (
                                                <div className="absolute inset-0 flex items-center justify-center bg-gray-200 animate-pulse z-10">
                                                    <svg className="w-8 h-8 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                    </svg>
                                                </div>
                                            )}
                                            <img
                                                key={`${attachment.id}-${attachment.optimized_path || attachment.path}`}
                                                src={getCdnUrl(getAttachmentUrl(attachment))}
                                                alt={attachment.filename}
                                                className={cn(
                                                    "w-full h-full object-cover aspect-square rounded-lg transition-transform duration-200 hover:scale-105",
                                                    "transition-opacity duration-500",
                                                    imgLoaded[attachment.id] ? "opacity-100" : "opacity-0"
                                                )}
                                                loading="lazy"
                                                onLoad={() => {
                                                    setImgLoaded(prev => ({ ...prev, [attachment.id]: true }));
                                                    if (rowVirtualizer?.measureElement && messageRef.current) {
                                                        rowVirtualizer.measureElement(messageRef.current);
                                                    }
                                                    if (rowVirtualizer?.measure) {
                                                        rowVirtualizer.measure();
                                                    }
                                                }}
                                            />
                                            {msg.attachments.length > 9 && index === 8 && (
                                                <div className="absolute inset-0 bg-black/50 flex items-center justify-center text-white font-medium">
                                                    +{msg.attachments.length - 9}
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    );
                case 'file':
                    return (
                        <div className="space-y-2">
                            {msg.content && (
                                <div
                                    className={cn(
                                        'whitespace-pre-wrap break-words',
                                        isCurrentUser
                                            ? 'text-white text-right'
                                            : 'text-black dark:text-white text-left'
                                    )}
                                >
                                    {msg.content}
                                </div>
                            )}
                            {msg.attachments && msg.attachments.map(attachment => (
                                <div
                                    key={attachment.id}
                                    className="flex items-center gap-3 p-3 bg-white/50 dark:bg-black rounded-lg border border-gray-200"
                                >
                                    <div className="flex-shrink-0">
                                        <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <p className="text-sm font-medium text-gray-900 truncate">
                                            {attachment.filename}
                                        </p>
                                        <p className="text-xs text-gray-500">
                                            {(attachment.size / 1024).toFixed(1)} KB
                                        </p>
                                    </div>
                                    <a
                                        href={getCdnUrl(attachment.original_path)}
                                        download={attachment.filename}
                                        className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 transition-colors"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                        </svg>
                                    </a>
                                </div>
                            ))}
                        </div>
                    );
                case 'gift':
                    return (
                        <GiftMessageBubble
                            msg={msg}
                            isCurrentUser={isCurrentUser}
                            partnerInfo={partnerInfo}
                        />
                    );
                case 'system':
                    return (
                        <div className="text-right text-black">
                            {msg.content || '[System message]'}
                        </div>
                    );
                default:
                    return (
                        <div
                            className={cn(
                                'whitespace-pre-wrap break-words',
                                isCurrentUser
                                    ? 'text-white text-right'
                                    : 'text-black dark:text-white text-left'
                            )}
                        >
                            {msg.content || '[Unsupported or unknown message type]'}
                        </div>
                    );
            }
        }, [msg.type, msg.content, msg.attachments, msg.gift_data, msg.sender?.nickname, isCurrentUser, imgLoaded]);

        const shouldRenderBubble =
            msg.content ||
            (msg.attachments && msg.attachments.length > 0) ||
            msg.gift_data ||
            ['text', 'image', 'file', 'gift', 'system'].includes(msg.type);

        return (
            <motion.div
                ref={messageRef}
                data-index={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3, type: 'spring', stiffness: 300, damping: 30 }}
                className={cn(
                    'py-1 px-2 group',
                    isHighlighted && 'bg-indigo-50/80 dark:bg-black'
                )}
                onContextMenu={handleContextMenu}
                id={`message-${msg.id}`}
            >
                <div className={cn(
                    'flex items-end gap-2',
                    isCurrentUser ? 'justify-end' : 'justify-start'
                )}>
                    {!isCurrentUser && (
                        <Avatar className="h-8 w-8 flex-shrink-0">
                            <AvatarImage
                                src={getCdnUrl(partnerInfo?.profile_image)}
                                alt={partnerInfo?.nickname || partnerInfo?.name}
                            />
                            <AvatarFallback>{partnerInfo?.nickname?.[0] || partnerInfo?.name?.[0] || '?'}</AvatarFallback>
                        </Avatar>
                    )}

                    <div className={cn(
                        'flex flex-col max-w-[80%]',
                        isCurrentUser ? 'items-end' : 'items-start'
                    )}>
                        {!isCurrentUser && msg.sender?.nickname && (
                            <span className="text-xs text-gray-500 mb-1">
                                {msg.sender.nickname}
                            </span>
                        )}
                        {shouldRenderBubble ? (
                            <motion.div
                                className={cn(
                                    'rounded-2xl px-4 py-3 text-sm relative overflow-hidden',
                                    'backdrop-blur-sm transition-all duration-200',
                                    isCurrentUser
                                        ? 'bg-gradient-to-br from-indigo-500 to-purple-600 dark:from-indigo-700 dark:to-purple-800 rounded-tr-md shadow-lg shadow-indigo-500/30 dark:shadow-indigo-900/40'
                                        : 'bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 border border-gray-200 dark:border-gray-800 rounded-tl-md shadow-lg shadow-gray-500/20 dark:shadow-black/30',
                                    msg.attachments && msg.attachments.length > 0 && 'mb-1',
                                    msg.type === 'system' && 'bg-gray-100 dark:bg-black text-gray-700 border border-gray-300'
                                )}
                                initial={{ scale: 0.95, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{
                                    duration: 0.2,
                                    type: 'spring',
                                    stiffness: 400,
                                    damping: 25
                                }}
                            >
                                {renderMessageContent()}
                                <div className={cn(
                                    'flex items-center text-[10px] mt-1',
                                    isCurrentUser ? 'justify-end text-indigo-100' : 'justify-start text-gray-500'
                                )}>
                                    <span>{formatMessageTime(msg.created_at)}</span>
                                    {isCurrentUser && (
                                        <span className="ml-1">
                                            {renderStatusIcon()}
                                        </span>
                                    )}
                                </div>
                            </motion.div>
                        ) : (
                            <div className="text-red-500 italic text-xs p-1">
                                [Empty message content]
                            </div>
                        )}
                    </div>
                </div>
            </motion.div>
        );
    }, (prevProps, nextProps) => {
        const prev = prevProps.msg;
        const next = nextProps.msg;
        return (
            prev.id === next.id &&
            prev.updated_at === next.updated_at &&
            prev.content === next.content &&
            prev.type === next.type &&
            attachmentsEqual(prev.attachments, next.attachments)
        );
    }
);

const ChatWindow = ({
    chat,
    onBack,
    onMessageSent,
    newConversationData = null,
    onConversationCreated = null,
    currentUserId = null
}) => {
    const { user } = useAuth();
    const { isOnline } = useNetwork();
    const { useBalance } = useWalletQuery();
    const { data: walletBalance = 0 } = useBalance();
    const location = useLocation();
    const cameFromProfileMessageRef = useRef(false);
    useEffect(() => {
        const params = new URLSearchParams(location.search);
        cameFromProfileMessageRef.current =
            params.get('action') === 'new_conversation' &&
            params.get('talent_id') &&
            !params.get('order_id');
    }, []);
    const [message, setMessage] = useState('');
    const [messages, setMessages] = useState([]);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [loading, setLoading] = useState(false);
    const [sending, setSending] = useState(false);
    const [unreadCount, setUnreadCount] = useState(0);
    const [showScrollButton, setShowScrollButton] = useState(false);
    const fileInputRef = useRef(null);
    const scrollAreaRef = useRef(null);
    const [attachments, setAttachments] = useState([]);
    const [dragOver, setDragOver] = useState(false);
    const [uploadProgress, setUploadProgress] = useState({});
    const [reactions, setReactions] = useState({});
    const dropZoneRef = useRef(null);
    const [readReceipts, setReadReceipts] = useState({});
    const [conversationState, setConversationState] = useState(
        newConversationData ? newConversationData.state : 'active'
    );
    const [isGiftModalOpen, setIsGiftModalOpen] = useState(false);
    const [receivedGift, setReceivedGift] = useState(null);
    const [showGiftNotification, setShowGiftNotification] = useState(false);
    const [partnerLastActiveAt, setPartnerLastActiveAt] = useState(() => {
        const info = getUserInfo(chat, currentUserId);
        return info?.last_seen_at ?? null;
    });
    // Add refs to track scroll position for pagination
    const prevScrollHeightRef = useRef(0);
    const prevFirstVisibleIndexRef = useRef(0);
    const prevFirstVisibleOffsetRef = useRef(0);
    // Add state for virtualized list
    const [isAtBottom, setIsAtBottom] = useState(true);
    const messagesContainerRef = useRef(null);
    const rowVirtualizer = useVirtualizer({
        count: messages.length,
        getScrollElement: () => messagesContainerRef.current,
        estimateSize: useCallback(() => 80, []),
        overscan: 5,
    });
    useEffect(() => {
        const info = getUserInfo(chat, currentUserId);
        setPartnerLastActiveAt(info?.last_seen_at ?? null);
    }, [chat, currentUserId]);
    // State to toggle quick reactions bar
    const [showQuickReactions, setShowQuickReactions] = useState(false);
    // Track when the initial message load completes so polling can start
    const [initialLoaded, setInitialLoaded] = useState(false);

    // Keep a ref of previous attachments so we can revoke object URLs when they
    // are removed from state
    const prevAttachmentsRef = useRef([]);

    useEffect(() => {
        prevAttachmentsRef.current.forEach(att => {
            if (!attachments.includes(att) && att.preview) {
                URL.revokeObjectURL(att.preview);
            }
        });
        prevAttachmentsRef.current = attachments;
    }, [attachments]);

    // Cleanup all previews on unmount
    useEffect(() => {
        return () => {
            prevAttachmentsRef.current.forEach(att => {
                if (att.preview) URL.revokeObjectURL(att.preview);
            });
        };
    }, []);

    const pendingOrderRef = useRef(false);

    useEffect(() => {
        const params = new URLSearchParams(location.search);
        const talentId = params.get('talent_id');
        const orderId = params.get('order_id');
        const pending = localStorage.getItem('pendingOrderMessage');

        if (!pending || pendingOrderRef.current) return;

        try {
            const orderMsg = JSON.parse(pending);
            if (
                String(orderMsg.talentId) === String(talentId) &&
                String(orderMsg.orderId) === String(orderId)
            ) {
                const send = () => {
                    setMessage(orderMsg.initialMessage);
                    setTimeout(() => {
                        handleSend();
                        localStorage.removeItem('pendingOrderMessage');
                        pendingOrderRef.current = true;
                    }, 50);
                };

                if (chat?.id && initialLoaded) {
                    send();
                } else if (conversationState !== 'active') {
                    setConversationState('composing');
                    send();
                }
            }
        } catch {
            localStorage.removeItem('pendingOrderMessage');
        }
    }, [chat?.id, initialLoaded, location.search, conversationState]);

    const markSentMessagesRead = useCallback(() => {
        setMessages(prev => {
            let updated = false;
            const mapped = prev.map(m => {
                const isSelf = String(m.sender_id) === String(currentUserId);
                const lastStatus = m.statuses && m.statuses.length > 0 ? m.statuses[m.statuses.length - 1].status : 'sent';
                if (isSelf && lastStatus !== 'read') {
                    updated = true;
                    const statuses = [...(m.statuses || []), { status: 'read', status_timestamp: new Date().toISOString() }];
                    return { ...m, statuses };
                }
                return m;
            });
            return updated ? mapped : prev;
        });
    }, [currentUserId]);

    useEffect(() => {
        const handleRealtimeMessage = (event) => {
            const { conversationId, message } = event.detail || {};
            if (String(conversationId) === String(chat?.id)) {
                if (String(message.sender_id) !== String(currentUserId)) {
                    markSentMessagesRead();
                }
                setMessages(prev => mergeAndSortMessages(prev, [normalizeMessage(message)]));
            }
        };
        window.addEventListener('realtime-chat-message', handleRealtimeMessage);
        return () => window.removeEventListener('realtime-chat-message', handleRealtimeMessage);
    }, [chat?.id, currentUserId, markSentMessagesRead]);

    // Messages per page returned by the backend
    const MESSAGES_PER_PAGE = 12;

    // --- PREVENT DUPLICATE API CALLS FOR PAGE 1 START ---
    // Track if loadMessages is running for page=1
    const loadPage1Ref = useRef(false);

    // --- SINGLE IN-FLIGHT FETCH GUARD START ---
    const isFetchingRef = useRef(false);
    // Track IDs of loaded messages for notification purposes
    const messageIdsRef = useRef(new Map());
    const pollIntervalRef = useRef(null);

    const { addChatNotification } = useNotifications();

    // Combined scroll effect to prevent infinite loops
    useEffect(() => {
        if (page === 1 && messages.length > 0) {
            const timeoutId = setTimeout(() => {
                try {
                    rowVirtualizer.scrollToIndex(messages.length - 1, { align: 'end' });
                } catch {
            rowVirtualizer.scrollToIndex(messages.length - 1);
        }
            }, 50);
            return () => clearTimeout(timeoutId);
        }
    }, [messages.length, page, rowVirtualizer]);

    // Keep track of loaded message IDs and updated_at for notification deduplication
    useEffect(() => {
        const map = new Map();
        messages.forEach(m => map.set(m.id, m.updated_at));
        messageIdsRef.current = map;
    }, [messages]);

    // Notify context about newly received messages
    const notifyNewMessages = useCallback((incoming) => {
        const partner = getUserInfo(chat, user?.id);
        const senderName = partner?.nickname || partner?.name || 'Someone';
        incoming.forEach(msg => {
            if (!messageIdsRef.current.has(msg.id) && String(msg.sender_id) !== String(user?.id)) {
                addChatNotification({
                    conversationId: msg.conversation_id,
                    senderId: msg.sender_id,
                    senderName,
                    content: msg.content || (msg.attachments && msg.attachments.length > 0 ? 'Sent an attachment' : '')
                });
            }
        });
    }, [chat, user?.id, addChatNotification]);

    // Load messages from API with proper ordering
    const loadMessages = useCallback(async () => {
        if (!chat?.id || !isOnline) return;
        setLoading(true);

        // Store scroll height before loading more messages (for page > 1)
        if (page > 1 && messagesContainerRef.current) {
            prevScrollHeightRef.current = messagesContainerRef.current.scrollHeight;
        }

        try {
            const response = await chatApi.getConversationMessages(chat.id, page, MESSAGES_PER_PAGE);
            const { data, next_page_url, user } = response.data;
            if (user?.last_active_at) {
                setPartnerLastActiveAt(user.last_active_at);
            }
            const orderedMessages = [...data].reverse().map(normalizeMessage);

            // Update message id map immediately to avoid treating
            // existing history as new messages when polling starts
            if (page === 1) {
                const map = new Map();
                orderedMessages.forEach(m => map.set(m.id, m.updated_at));
                messageIdsRef.current = map;
            } else {
                orderedMessages.forEach(m => {
                    messageIdsRef.current.set(m.id, m.updated_at);
                });
            }
            
            setHasMore(next_page_url !== null);

            setMessages(prev => {
                if (page === 1) {
                    return orderedMessages;
                } else {
                    // Prepend older messages and deduplicate by id
                    return mergeAndSortMessages(prev, orderedMessages);
                }
            });

            if (orderedMessages.some(m => String(m.sender_id) !== String(currentUserId))) {
                markSentMessagesRead();
            }

            if (page === 1) setInitialLoaded(true);
        } catch (error) {
            console.error('Error loading messages:', error);
            if (page === 1) setMessages([]);
        } finally {
            setLoading(false);
        }
    }, [chat?.id, page, isOnline, currentUserId, markSentMessagesRead]);

    useEffect(() => {
        if (chat?.id && conversationState === 'active') {
            setMessages([]);
            setPage(1);
            setHasMore(true);
            setInitialLoaded(false);
            setLoading(true); // Ensure loading is true so the loader shows until the fetch completes
        }
    }, [chat?.id, conversationState]);

    // Main effect to handle message loading
    useEffect(() => {
        if (chat?.id && isOnline) {
            loadMessages();
        }
    }, [chat?.id, page, isOnline, loadMessages]);

    // Memoize pollMessages to avoid unnecessary re-creations
    const pollMessages = useCallback(async () => {
        if (!chat?.id || !isOnline || isFetchingRef.current) return;
        if (!initialLoaded) return;
        if (loadPage1Ref.current) return;
        isFetchingRef.current = true;
        try {
            const response = await chatApi.getConversationMessages(chat.id, 1, MESSAGES_PER_PAGE);
            const { data, user } = response.data;
            if (user?.last_active_at) {
                setPartnerLastActiveAt(user.last_active_at);
            }
            const newMessages = [...data].reverse();
            newMessages.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
            if (newMessages.some(m => !messageIdsRef.current.has(m.id) || m.updated_at !== messageIdsRef.current.get(m.id))) {
                notifyNewMessages(newMessages);
                if (newMessages.some(m => String(m.sender_id) !== String(currentUserId))) {
                    markSentMessagesRead();
                }
                setMessages(prev => mergeAndSortMessages(prev, newMessages));
            }
        } catch (error) {
            console.error('Error polling messages:', error);
        } finally {
            isFetchingRef.current = false;
        }
    }, [chat?.id, isOnline, initialLoaded, notifyNewMessages, markSentMessagesRead, currentUserId]);

    // Polling effect with Page Visibility API
    useEffect(() => {
        if (!chat?.id || !isOnline || !initialLoaded) return;

        const startPolling = () => {
            pollMessages();
            pollIntervalRef.current = setInterval(pollMessages, 5000);
        };

        const stopPolling = () => {
            if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current);
                pollIntervalRef.current = null;
            }
        };

        const handleVisibilityChange = () => {
            if (document.visibilityState === 'visible') {
                startPolling();
            } else {
                stopPolling();
            }
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);

        // Start polling if visible
        if (document.visibilityState === 'visible') {
            startPolling();
        }

        return () => {
            stopPolling();
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    }, [chat?.id, isOnline, pollMessages, initialLoaded]);

    // Cleanup effect to prevent memory leaks and improve stability
    useEffect(() => {
        return () => {
            // Cleanup polling on unmount
            if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current);
                pollIntervalRef.current = null;
            }
            // Cleanup any pending uploads
            setUploadProgress({});
            // Reset fetching flags
            isFetchingRef.current = false;
            loadPage1Ref.current = false;
        };
    }, []);

    const handleSend = async () => {
        if ((!message.trim() && attachments.length === 0) || sending) return;

        setSending(true);

        try {
            if (conversationState === 'composing' && newConversationData) {
                const conversationResponse = await chatApi.startConversation(
                    newConversationData.talentId,
                    null,
                    message.trim()
                );

                if (conversationResponse && conversationResponse.data) {
                    setConversationState('active');

                    if (onConversationCreated) {
                        onConversationCreated(conversationResponse.data);
                    }

                    setMessages([{
                        id: conversationResponse.data.last_message?.id || Date.now(),
                        conversation_id: conversationResponse.data.id,
                        sender_id: user?.id,
                        content: message.trim(),
                        created_at: new Date().toISOString(),
                        type: 'text'
                    }]);

                    setMessage('');
                    setAttachments([]);
                    setSending(false);
                    if (cameFromProfileMessageRef.current) {
                        window.location.reload();
                    }
                    return;
                } else {
                    throw new Error('Failed to create conversation');
                }
            }

            // Create temporary message for optimistic UI update
            // Construct an optimistic message so users immediately see their image
            // While waiting for the server response. Normalize it so `type` is
            // correctly inferred and no placeholder text is shown.
            const generateTempId = () => `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            const tempMessageId = generateTempId();
            
            const tempMessage = normalizeMessage({
                id: tempMessageId,
                conversation_id: chat.id,
                sender_id: localStorage.getItem('user_id'),
                is_self: true,
                content: message.trim(),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                attachments: attachments.map((att) => ({
                    id: `temp-att-${tempMessageId}-${att.id}`,
                    message_id: tempMessageId,
                    optimized_path: att.preview,
                    filename: att.file.name,
                    size: att.file.size,
                    mime_type: att.file.type,
                    dimensions: { width: 0, height: 0 }
                })),
                statuses: [
                    {
                        status: 'sent',
                        status_timestamp: new Date().toISOString()
                    }
                ]
            });

            // Add temporary message to UI immediately
            setMessages(prev => [...prev, tempMessage]);

            // Handle attachments if present
            if (attachments.length > 0) {
                for (let i = 0; i < attachments.length; i++) {
                    const attachment = attachments[i];
                    try {
                        // Update progress in state
                        const updateProgress = (progress) => {
                            setUploadProgress(prev => ({
                                ...prev,
                                [attachment.file.name]: progress
                            }));
                        };

                        const response = await chatApi.sendAttachmentMessage(
                            chat.id,
                            message.trim(),
                            [attachment.file],
                            updateProgress
                        );

                        const normalized = normalizeMessage(response.data);
                        if (i === 0) {
                            // Replace temp message with the first real message
                            setMessages(prev => prev.map(msg =>
                                msg.id === tempMessage.id ? { ...normalized, _replaced: true } : msg
                            ));
                        } else {
                            // Append additional attachment messages
                            setMessages(prev => [...prev, { ...normalized, _replaced: true }]);
                        }

                        // Notify parent component about sent message
                        onMessageSent?.(normalized);
                    } catch (error) {
                        console.error('Error sending attachment:', error);
                        if (i === 0) {
                            // Mark the temp message as failed
                            setMessages(prev => prev.map(msg =>
                                msg.id === tempMessage.id
                                    ? { ...msg, sendError: true, errorMessage: error.message || 'Failed to send attachment' }
                                    : msg
                            ));
                        } else {
                            // Append a failed placeholder for this attachment
                            setMessages(prev => [
                                ...prev,
                                {
                                    id: `failed-${Date.now()}`,
                                    conversation_id: chat.id,
                                    sender_id: user?.id,
                                    content: message.trim(),
                                    created_at: new Date().toISOString(),
                                    attachments: [],
                                    sendError: true,
                                    errorMessage: error.message || 'Failed to send attachment'
                                }
                            ]);
                        }
                    }
                }

                setAttachments([]);
            }

            // Send text message if present
            if (message.trim()) {
                try {
                    const response = await chatApi.sendTextMessage(chat.id, message.trim());

                    const normalized = normalizeMessage(response.data);

                    // Replace temp message with real message from server if no attachments were sent
                    if (attachments.length === 0) {
                        setMessages(prev => prev.map(msg =>
                            msg.id === tempMessage.id ? { ...normalized, _replaced: true } : msg
                        ));
                    }

                    // Notify parent component about sent message
                    onMessageSent?.(normalized);
                } catch (error) {
                    console.error('Error sending message:', error);
                    // Mark the message as failed
                    setMessages(prev => prev.map(msg =>
                        msg.id === tempMessage.id && attachments.length === 0
                            ? { ...msg, sendError: true, errorMessage: error.message || 'Failed to send message' }
                            : msg
                    ));
                }
            }

            setMessage('');
            scrollToBottom();
        } catch (error) {
            console.error('Error in handleSend:', error);
            if (conversationState === 'composing') {
                toast.error('Failed to start conversation. Please try again.');
            } else {
                toast.error('Failed to send message. Please try again.');
            }
        } finally {
            setSending(false);
            setUploadProgress({});
        }
    };

    // Handle attachment upload
    const handleAttachment = async (files) => {
        try {
            // Convert FileList to Array
            const fileArray = Array.from(files);

            // Check file types and sizes
            const validFileTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/heic', 'image/heif'];
            const maxFileSize = 10 * 1024 * 1024; // 10MB

            // Validate each file
            for (const file of fileArray) {
                if (!validFileTypes.includes(file.type)) {
                    console.error(`Invalid file type: ${file.type}`);
                    continue;
                }

                if (file.size > maxFileSize) {
                    console.error(`File too large: ${file.size / 1024 / 1024}MB`);
                    continue;
                }
            }

            // Process each valid file
            const processedFiles = await Promise.all(
                fileArray.map(async (file) => {
                    try {
                        // Create a preview immediately for better UX
                        const preview = URL.createObjectURL(file);

                        // For image files, compress them
                        let compressedFile = file;

                        // Skip GIFs from compression to maintain animation
                        if (file.type.startsWith('image/') && file.type !== 'image/gif') {
                            // Create a promise for image loading
                            const loadImagePromise = new Promise((resolve) => {
                                const img = new Image();
                                img.onload = () => resolve(img);
                                img.src = preview;
                            });

                            // Wait for image to load
                            const img = await loadImagePromise;

                            // Calculate new dimensions (max 1200px width/height while maintaining aspect ratio)
                            const maxSize = 1200;
                            let width = img.width;
                            let height = img.height;

                            if (width > height && width > maxSize) {
                                height = (height * maxSize) / width;
                                width = maxSize;
                            } else if (height > maxSize) {
                                width = (width * maxSize) / height;
                                height = maxSize;
                            }

                            // Create canvas for resizing
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            // Set canvas dimensions
                            canvas.width = width;
                            canvas.height = height;

                            // Draw image on canvas with new dimensions
                            ctx.drawImage(img, 0, 0, width, height);

                            // Convert to blob with reduced quality for JPG/JPEG
                            const format = file.type === 'image/jpeg' ? 'image/jpeg' : 'image/webp';
                            const quality = file.type === 'image/jpeg' ? 0.8 : 0.85;

                            // Convert canvas to blob
                            const blob = await new Promise(resolve =>
                                canvas.toBlob(resolve, format, quality)
                            );

                            // Create new File object from blob
                            compressedFile = new File([blob], file.name, {
                                type: format,
                                lastModified: Date.now()
                            });
                        }

                        return {
                            id: `${Date.now()}-${Math.random().toString(36).slice(2, 8)}`,
                            file: compressedFile,
                            preview,
                            progress: 0,
                            originalSize: file.size,
                            compressedSize: compressedFile.size,
                            compressionRatio: Math.round((1 - (compressedFile.size / file.size)) * 100)
                        };
                    } catch (error) {
                        console.error('Error compressing image:', error);
                        // If compression fails, return original file
                        return {
                            id: `${Date.now()}-${Math.random().toString(36).slice(2, 8)}`,
                            file,
                            preview: URL.createObjectURL(file),
                            progress: 0,
                            originalSize: file.size,
                            compressedSize: file.size,
                            compressionRatio: 0
                        };
                    }
                })
            );

            // Add to attachments state
            setAttachments(prev => [...prev, ...processedFiles]);

            // Log compression statistics
            // Log compression statistics in development only
            if (process.env.NODE_ENV !== 'production') {
            processedFiles.forEach(file => {
                if (file.compressionRatio > 0) {
                    console.log(`Compressed ${file.file.name}: ${file.compressionRatio}% reduction (${(file.originalSize / 1024).toFixed(1)}KB → ${(file.compressedSize / 1024).toFixed(1)}KB)`);
                }
            });
            }
        } catch (error) {
            console.error('Error processing attachments:', error);
        }
    };

    // Update scroll handling logic
    const handleScrollChange = useCallback((scrollInfo) => {
        setShowScrollButton(!scrollInfo.isNearBottom);
        setIsAtBottom(scrollInfo.isNearBottom);

        // Load more messages if needed
        if (scrollInfo.scrollTop < 100 && hasMore && !loading) {
            // Record scroll height and first visible message offset
            if (messagesContainerRef.current && rowVirtualizer) {
                prevScrollHeightRef.current = messagesContainerRef.current.scrollHeight;
                const virtualItems = rowVirtualizer.getVirtualItems();
                if (virtualItems.length > 0) {
                    prevFirstVisibleIndexRef.current = virtualItems[0].index;
                    const firstNode = messagesContainerRef.current.querySelector(`[data-index='${virtualItems[0].index}']`);
                    prevFirstVisibleOffsetRef.current = firstNode ? firstNode.getBoundingClientRect().top : 0;
                }
            }
            setPage(prev => prev + 1);
        }

        // Update unread count if not at bottom
        if (!scrollInfo.isNearBottom) {
            // Check for unread messages that are not visible
            const unreadMessages = messages.filter(m =>
                m.sender_id !== localStorage.getItem('user_id') &&
                !readReceipts[m.id]
            );
            setUnreadCount(unreadMessages.length);
        } else {
            // Reset unread count when at bottom
            setUnreadCount(0);
        }
    }, [messages, hasMore, loading, readReceipts]);

    // Scroll to bottom of messages
    const scrollToBottom = () => {
        if (messagesContainerRef.current) {
            messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
        setUnreadCount(0);
    };

    // When a conversation is selected, scroll to the latest message once
    useEffect(() => {
        if (
            chat?.id &&
            initialLoaded &&
            page === 1 &&
            !loading &&
            messages.length > 0 &&
            messagesContainerRef.current
        ) {
            messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
    }, [chat?.id, initialLoaded, page, messages.length, loading]);

    // Ensure the chat window snaps to the newest message when switching conversations
    useEffect(() => {
        if (!chat?.id || !initialLoaded) return;
        const timer = setTimeout(() => {
            try {
                rowVirtualizer.scrollToIndex(messages.length - 1, { align: 'end' });
            } catch {
                rowVirtualizer.scrollToIndex(messages.length - 1);
            }
            scrollToBottom();
        }, 100);
        return () => clearTimeout(timer);
    }, [chat?.id, initialLoaded, messages.length, rowVirtualizer]);

    // Preserve scroll position when prepending
    useLayoutEffect(() => {
        if (page > 1 && messagesContainerRef.current && prevScrollHeightRef.current) {
            const el = messagesContainerRef.current;
            const newScrollHeight = el.scrollHeight;
            el.scrollTop = newScrollHeight - prevScrollHeightRef.current;
            prevScrollHeightRef.current = 0; // Reset after use
        }
    }, [messages, page]);

    // Handle file drop
    const handleDrop = useCallback((e) => {
        e.preventDefault();
        setDragOver(false);
        const files = Array.from(e.dataTransfer.files);
        const imageFiles = files.filter(file => file.type.startsWith('image/'));
        if (imageFiles.length > 0) {
            handleAttachment(imageFiles);
        }
    }, []);

    // Add a function to handle sending gifts
    const handleSendGift = async (giftMessage) => {
        try {
            // Normalize the backend response
            const normalized = normalizeMessage(giftMessage);
            // Merge and sort into the chat
            setMessages(prev => mergeAndSortMessages(prev, [normalized]));
            // Optionally scroll to bottom
            scrollToBottom();
        } catch (error) {
            console.error('Error handling sent gift:', error);
        } finally {
            setIsGiftModalOpen(false);
        }
    };

    // Add scroll handler
    const handleScroll = useCallback((e) => {
        const { scrollTop } = e.target;
        if (scrollTop < 100 && hasMore && !loading) {
            setPage(prev => prev + 1);
        }
    }, [hasMore, loading]);

    // Quick reactions UI handler
    const handleQuickReaction = async (reaction) => {
        if (sending) return;
        setSending(true);
        try {
            const response = await chatApi.sendTextMessage(chat.id, reaction);
            const normalized = normalizeMessage(response.data);
            setMessages(prev => [...prev, normalized]);
            onMessageSent?.(normalized);
            scrollToBottom();
            setShowQuickReactions(false); // auto-hide after sending
        } catch (error) {
            toast.error('Failed to send reaction. Please try again.');
        } finally {
            setSending(false);
        }
    };

    const renderPreparingState = () => (
        <div className="flex flex-col h-full w-full justify-center items-center bg-gray-50 dark:bg-black p-8">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center max-w-md mx-auto"
            >
                <Avatar className="h-20 w-20 mx-auto mb-4">
                    <AvatarImage
                        src={getCdnUrl(newConversationData?.talentImage)}
                        alt={newConversationData?.talentName}
                    />
                    <AvatarFallback className="text-2xl">
                        {newConversationData?.talentName?.[0] || '?'}
                    </AvatarFallback>
                </Avatar>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-indigo-100 mb-2">
                    About to message {newConversationData?.talentName}
                </h2>
                <p className="text-gray-600 dark:text-indigo-100 mb-6">
                    Start typing your message below to begin the conversation.
                </p>
                <Button
                    onClick={() => setConversationState('composing')}
                    className="bg-gradient-to-r from-indigo-500 to-purple-600 dark:bg-indigo-500 dark:text-indigo-100 text-white px-6 py-3 rounded-full"
                >
                    Start Conversation
                </Button>
            </motion.div>
        </div>
    );

    const renderComposingState = () => (
        <div className="flex flex-col h-full w-full bg-gray-50 dark:bg-black">
            <div className="flex items-center justify-between p-4 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 shadow-sm">
                <div className="flex items-center space-x-3">
                    <Button
                        variant="ghost"
                        size="icon"
                        className="lg:hidden"
                        onClick={onBack}
                    >
                        <ArrowLeftIcon className="w-5 h-5" />
                    </Button>

                    {/* Get conversation partner info */}
                    {(() => {
                        const partnerInfo = getUserInfo(chat, currentUserId);
                        const profileImageUrl = partnerInfo?.profile_image ? getCdnUrl(partnerInfo.profile_image) : null;

                        // Debug logging for profile image issues
                        if (process.env.NODE_ENV !== 'production' && !partnerInfo) {
                            console.warn(`No partner info found for chat ${chat.id} in ChatWindow`, {
                                chat,
                                currentUserId,
                                participants: chat.participants,
                                chat_partner: chat.chat_partner,
                                user: chat.user,
                                talent: chat.talent,
                                availableKeys: Object.keys(chat)
                            });
                        }

                        return (
                            <>
                                <Avatar className="h-10 w-10">
                                    <AvatarImage
                                        src={profileImageUrl}
                                        alt={partnerInfo?.nickname || partnerInfo?.name || 'User'}
                                    />
                                    <AvatarFallback className="bg-gradient-to-br from-indigo-100 to-violet-100 text-indigo-600 font-semibold">
                                        {partnerInfo?.nickname?.[0] || partnerInfo?.name?.[0] || '?'}
                                    </AvatarFallback>
                                </Avatar>

                                <div className="text-left">
                                    <h2 className="font-semibold text-gray-900 flex items-center space-x-2">
                                        <span>{partnerInfo?.nickname || partnerInfo?.name || 'Unknown User'}</span>
                                        {partnerInfo?.uid && (
                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                                {partnerInfo.uid}
                                            </span>
                                        )}
                                    </h2>

                                    <p className="text-sm text-left text-gray-500 mt-1">
                                        {partnerInfo?.is_online ? 'Online' : 'Last seen ' + formatLastSeen(partnerLastActiveAt || partnerInfo?.last_seen_at)}
                                    </p>
                                </div>
                            </>
                        );
                    })()}
                </div>
            </div>

            <div className="flex-1 flex items-center justify-center">
                <div className="text-center text-gray-500 dark:text-gray-400">
                    <p>Send your first message to start the conversation</p>
                </div>
            </div>

            <div className="p-4 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-t border-gray-100 dark:border-gray-800">
                <div className="flex items-end space-x-3">
                    <div className="flex-1 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-800 rounded-2xl shadow-lg border border-gray-200/50 dark:border-gray-700 p-3 backdrop-blur-sm">
                        <div className="relative">
                            <Textarea
                                aria-label="Message input"
                                value={message}
                                onChange={(e) => {
                                    setMessage(e.target.value);
                                }}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter' && !e.shiftKey) {
                                        e.preventDefault();
                                        handleSend();
                                    }
                                }}
                                placeholder="Type a message..."
                                className="w-full bg-transparent border-0 focus:ring-0 resize-none
                                        p-3 max-h-32 text-sm placeholder-gray-400 dark:text-gray-100 dark:placeholder-gray-500 pr-10 min-h-[48px]
                                        focus:placeholder-gray-300 dark:focus:placeholder-gray-400 transition-colors duration-200"
                                rows={1}
                                autoFocus
                            />

                            {/* Message character counter - shows when typing */}
                            {message.length > 0 && (
                                <div className="absolute right-2 bottom-2 text-xs text-gray-400">
                                    {message.length > 1000 ? (
                                        <span className={message.length > 1000 ? "text-red-500" : "text-amber-500"}>
                                            {message.length}/1000
                                        </span>
                                    ) : null}
                                </div>
                            )}
                        </div>

                        <div className="flex items-center justify-between px-2 border-t border-gray-200 dark:border-gray-700 mt-2 pt-2">
                            <div className="flex items-center space-x-3">
                                {/* File attachment button */}
                                <div className="relative group">
                                    <input
                                        type="file"
                                        ref={fileInputRef}
                                        onChange={(e) => handleAttachment(e.target.files)}
                                        accept="image/*"
                                        className="hidden"
                                        multiple
                                    />
                                    <button
                                        onClick={() => fileInputRef.current?.click()}
                                        className="p-2 text-indigo-500 bg-indigo-50 dark:bg-gray-800 hover:text-gray-600 dark:hover:text-gray-300
                                            rounded-full hover:bg-indigo-50 dark:hover:bg-gray-700 transition-colors flex items-center justify-center"
                                        disabled={sending}
                                        title="Attach images"
                                    >
                                        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </button>
                                    <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 dark:bg-gray-700 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        Attach images
                                    </span>
                                </div>

                                {/* Gift button */}
                                <div className="relative group">
                                    <button
                                        onClick={() => {
                                            setIsGiftModalOpen(true);
                                        }}
                                        className="p-2 text-indigo-500 bg-indigo-50 dark:bg-gray-800 hover:text-pink-600 dark:hover:text-pink-400
                                            rounded-full hover:bg-pink-50 dark:hover:bg-pink-900 transition-colors flex items-center justify-center"
                                        title="Send a gift"
                                        disabled={sending}
                                    >
                                        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                                        </svg>
                                    </button>
                                    <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 dark:bg-gray-700 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        Send a gift
                                    </span>
                                </div>

                                {/* Emoji picker */}
                                <div className="relative group">
                                    <button
                                        className="p-2 text-indigo-500 bg-indigo-50 dark:bg-gray-800 hover:text-gray-600 dark:hover:text-gray-300
                                            rounded-full hover:bg-indigo-50 dark:hover:bg-gray-700 transition-colors flex items-center justify-center"
                                        title="Quick reactions"
                                        type="button"
                                        onClick={() => setShowQuickReactions((prev) => !prev)}
                                    >
                                        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </button>
                                    <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 dark:bg-gray-700 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        Quick reactions
                                    </span>
                                </div>
                            </div>

                            {/* Send button */}
                            <motion.button
                                aria-label="Send message"
                                onClick={handleSend}
                                disabled={(!message.trim() && attachments.length === 0) || sending}
                                className={cn(
                                    "px-6 py-3 rounded-full text-sm font-semibold transition-all duration-300 flex items-center justify-center relative overflow-hidden",
                                    "min-w-[80px] h-12",
                                    message.trim() || attachments.length > 0
                                        ? "bg-gradient-to-r from-indigo-500 via-purple-500 to-indigo-600 text-white shadow-lg shadow-indigo-500/30"
                                        : "bg-gray-200 dark:bg-gray-800 text-gray-400 cursor-not-allowed"
                                )}
                                whileHover={
                                    (message.trim() || attachments.length > 0) && !sending
                                        ? {
                                            scale: 1.05,
                                            boxShadow: "0 20px 25px -5px rgba(99, 102, 241, 0.4), 0 10px 10px -5px rgba(99, 102, 241, 0.04)"
                                        }
                                        : {}
                                }
                                whileTap={
                                    (message.trim() || attachments.length > 0) && !sending
                                        ? { scale: 0.95 }
                                        : {}
                                }
                                transition={{ type: "spring", stiffness: 400, damping: 25 }}
                            >
                                {/* Animated background gradient */}
                                {(message.trim() || attachments.length > 0) && !sending && (
                                    <motion.div
                                        className="absolute inset-0 bg-gradient-to-r from-purple-500 via-indigo-500 to-purple-500"
                                        animate={{
                                            x: ["-100%", "100%"],
                                        }}
                                        transition={{
                                            duration: 3,
                                            repeat: Infinity,
                                            ease: "linear"
                                        }}
                                    />
                                )}
                                <div className="relative z-10 flex items-center">
                                    {sending ? (
                                        <motion.span
                                            className="flex items-center"
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1 }}
                                        >
                                            <motion.svg
                                                className="w-4 h-4 text-white mr-2"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                animate={{ rotate: 360 }}
                                                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                            >
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </motion.svg>
                                            Sending
                                        </motion.span>
                                    ) : (
                                        <motion.span
                                            className="flex items-center"
                                            whileHover={{ x: 2 }}
                                            transition={{ type: "spring", stiffness: 400, damping: 25 }}
                                        >
                                            Send
                                            <motion.svg
                                                className="ml-2 w-4 h-4"
                                                viewBox="0 0 20 20"
                                                fill="currentColor"
                                                whileHover={{ x: 3, rotate: -15 }}
                                                transition={{ type: "spring", stiffness: 400, damping: 25 }}
                                            >
                                                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                                            </motion.svg>
                                        </motion.span>
                                    )}
                                </div>
                            </motion.button>
                        </div>

                        {/* Display attachments preview */}
                        {attachments.length > 0 && (
                            <div className="flex flex-wrap gap-2 mt-2 pt-2 border-t border-gray-200">
                                {attachments.map((attachment) => (
                                    <div key={attachment.id} className="relative group">
                                        <img
                                            src={attachment.preview}
                                            alt="Attachment preview"
                                            className="h-16 w-16 rounded-md object-cover border border-gray-200"
                                        />
                                        <button
                                            onClick={() => setAttachments(prev => prev.filter(a => a.id !== attachment.id))}
                                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
                                        >
                                            ×
                                        </button>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );

    if (conversationState === 'preparing') {
        return renderPreparingState();
    }

    if (conversationState === 'composing') {
        return renderComposingState();
    }

    return (
        <div className="flex flex-col h-full w-full bg-gray-50 dark:bg-gray-950 relative pb-24 sm:pb-0">
            {/* Overlay Quick Reactions Bar - above input area */}
            {showQuickReactions && (
                <div
                    className="absolute left-0 right-0 z-30 flex justify-center"
                    style={{ bottom: '180px', pointerEvents: 'none' }}
                >
                    <div
                        className="flex gap-3 bg-white/80 dark:bg-gray-900/80 rounded-full px-4 py-2 shadow border border-gray-100 dark:border-gray-800 pointer-events-auto"
                        style={{ pointerEvents: 'auto' }}
                    >
                        {QUICK_REACTIONS.map((reaction) => (
                            <button
                                key={reaction}
                                type="button"
                                className="text-xl bg-transparent hover:scale-125 transition-transform duration-150 focus:outline-none"
                                onClick={() => handleQuickReaction(reaction)}
                                disabled={sending}
                                aria-label={`Send quick reaction ${reaction}`}
                                style={{ background: 'transparent' }}
                            >
                                {reaction}
                            </button>
                        ))}
                    </div>
                </div>
            )}
            {/* Header */}
            <div className="flex items-center justify-between p-4 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 shadow-sm">
                <div className="flex items-center space-x-3">
                    <Button
                        variant="ghost"
                        size="icon"
                        className="lg:hidden bg-transparent"
                        onClick={onBack}
                    >
                        <ArrowLeftIcon className="w-5 h-5" />
                    </Button>

                    {/* Get conversation partner info */}
                    {(() => {
                        const partnerInfo = getUserInfo(chat, currentUserId);
                        const profileImageUrl = partnerInfo?.profile_image ? getCdnUrl(partnerInfo.profile_image) : null;

                        // Debug logging for profile image issues
                        if (process.env.NODE_ENV !== 'production' && !partnerInfo) {
                            console.warn(`No partner info found for chat ${chat.id} in ChatWindow`, {
                                chat,
                                currentUserId,
                                participants: chat.participants,
                                chat_partner: chat.chat_partner,
                                user: chat.user,
                                talent: chat.talent,
                                availableKeys: Object.keys(chat)
                            });
                        }

                        return (
                            <>
                                <Avatar className="h-10 w-10">
                                    <AvatarImage
                                        src={profileImageUrl}
                                        alt={partnerInfo?.nickname || partnerInfo?.name || 'User'}
                                    />
                                    <AvatarFallback className="bg-gradient-to-br from-indigo-100 to-violet-100 dark:from-indigo-900 dark:to-violet-900 text-indigo-600 dark:text-indigo-200 font-semibold">
                                        {partnerInfo?.nickname?.[0] || partnerInfo?.name?.[0] || '?'}
                                    </AvatarFallback>
                                </Avatar>

                                <div className="text-left">
                                    <h2 className="font-semibold text-gray-900 dark:text-gray-100">
                                        {partnerInfo?.nickname || partnerInfo?.name || 'Unknown User'}
                                    </h2>
                                    <p className="text-sm text-left text-gray-500 dark:text-gray-300">
                                        {partnerInfo?.is_online ? 'Online' : 'Last seen ' + formatLastSeen(partnerLastActiveAt || partnerInfo?.last_seen_at)}
                                    </p>
                                </div>
                            </>
                        );
                    })()}
                </div>
            </div>

            {/* Messages */}
            <div
                ref={messagesContainerRef}
                style={{
                    flex: 1,
                    overflowY: 'auto',
                    display: 'flex',
                    flexDirection: 'column',
                    padding: '16px',
                    position: 'relative',
                    minHeight: 0,
                }}
                className="bg-gray-50 dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-950"
                onScroll={handleScroll}
            >
                {/* Loader for older messages at the top */}
                {loading && page > 1 && (
                    <div style={{ textAlign: 'center', padding: 8 }}>
                        <SectionLoader message="Loading more..." size="small" color="indigo" />
                    </div>
                )}
                {/* Messages */}
                {messages.map((msg, idx) => {
                    const prev = messages[idx - 1];
                    const showDateSeparator =
                        idx === 0 ||
                        DateTime.fromISO(msg.created_at).startOf('day').toISODate() !==
                            DateTime.fromISO(prev.created_at).startOf('day').toISODate();

                    return (
                        <React.Fragment key={`${msg.id}-${msg.updated_at}`}>
                            {showDateSeparator && (
                                <div className="flex justify-center my-4">
                                    <span className="px-3 py-1 text-xs rounded-full bg-gray-200 dark:bg-gray-800/80 text-gray-600 dark:text-gray-200">
                                        {formatMessageDate(msg.created_at)}
                                    </span>
                                </div>
                            )}
                            <MessageItem
                                msg={msg}
                                chat={chat}
                                index={idx}
                            />
                        </React.Fragment>
                    );
                })}
                {/* Loader for initial load (centered) */}
                {loading && page === 1 && (
                    <SectionLoader message="Loading messages..." size="medium" color="indigo" />
                )}
            </div>

            {/* Input Area */}
            <div className="p-4 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-t border-gray-100 dark:border-gray-800">
                <div className="flex items-end space-x-3">
                    <div className="flex-1 bg-gradient-to-r from-gray-50 to-white dark:from-gray-900 dark:to-gray-950 rounded-2xl shadow-lg border border-gray-200/50 dark:border-gray-800 p-3 backdrop-blur-sm">
                        <div className="relative">
                            <Textarea
                                aria-label="Message input"
                                value={message}
                                onChange={(e) => {
                                    setMessage(e.target.value);
                                }}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter' && !e.shiftKey) {
                                        e.preventDefault();
                                        handleSend();
                                    }
                                }}
                                placeholder="Type a message..."
                                className="w-full bg-transparent border-0 focus:ring-0 resize-none p-3 max-h-32 text-sm placeholder-gray-400 dark:placeholder-gray-500 pr-10 min-h-[48px] focus:placeholder-gray-300 transition-colors duration-200 dark:text-gray-100"
                                rows={1}
                                autoFocus
                            />

                            {/* Message character counter - shows when typing */}
                            {message.length > 0 && (
                                <div className="absolute right-2 bottom-2 text-xs text-gray-400 dark:text-gray-500">
                                    {message.length > 1000 ? (
                                        <span className={message.length > 1000 ? "text-red-500" : "text-amber-500"}>
                                            {message.length}/1000
                                        </span>
                                    ) : null}
                                </div>
                            )}
                        </div>

                        <div className="flex items-center justify-between px-2 border-t border-gray-200 dark:border-gray-800 mt-2 pt-2">
                            <div className="flex items-center space-x-3">
                                {/* File attachment button */}
                                <div className="relative group">
                                    <input
                                        type="file"
                                        ref={fileInputRef}
                                        onChange={(e) => handleAttachment(e.target.files)}
                                        accept="image/*"
                                        className="hidden"
                                        multiple
                                    />
                                    <button
                                        onClick={() => fileInputRef.current?.click()}
                                        className="p-2 text-indigo-500 bg-indigo-50 dark:bg-gray-800 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-indigo-50 dark:hover:bg-gray-700 transition-colors flex items-center justify-center"
                                        disabled={sending}
                                        title="Attach images"
                                    >
                                        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </button>
                                    <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 dark:bg-gray-700 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        Attach images
                                    </span>
                                </div>

                                {/* Gift button */}
                                <div className="relative group">
                                    <button
                                        onClick={() => {
                                            setIsGiftModalOpen(true);
                                        }}
                                        className="p-2 text-indigo-500 bg-indigo-50 dark:bg-gray-800 hover:text-pink-600 dark:hover:text-pink-400 rounded-full hover:bg-pink-50 dark:hover:bg-pink-900 transition-colors flex items-center justify-center"
                                        title="Send a gift"
                                        disabled={sending}
                                    >
                                        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                                        </svg>
                                    </button>
                                    <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 dark:bg-gray-700 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        Send a gift
                                    </span>
                                </div>

                                {/* Emoji picker */}
                                <div className="relative group">
                                    <button
                                        className="p-2 text-indigo-500 bg-indigo-50 dark:bg-gray-800 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-indigo-50 dark:hover:bg-gray-700 transition-colors flex items-center justify-center"
                                        title="Quick reactions"
                                        type="button"
                                        onClick={() => setShowQuickReactions((prev) => !prev)}
                                    >
                                        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </button>
                                    <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 dark:bg-gray-700 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        Quick reactions
                                    </span>
                                </div>
                            </div>

                            {/* Send button */}
                            <motion.button
                                aria-label="Send message"
                                onClick={handleSend}
                                disabled={(!message.trim() && attachments.length === 0) || sending}
                                className={cn(
                                    "px-6 py-3 rounded-full text-sm font-semibold transition-all duration-300 flex items-center justify-center relative overflow-hidden",
                                    "min-w-[80px] h-12",
                                    message.trim() || attachments.length > 0
                                        ? "bg-gradient-to-r from-indigo-500 via-purple-500 to-indigo-600 text-white shadow-lg shadow-indigo-500/30"
                                        : "bg-gray-200 dark:bg-gray-800 text-gray-400 cursor-not-allowed"
                                )}
                                whileHover={
                                    (message.trim() || attachments.length > 0) && !sending
                                        ? {
                                            scale: 1.05,
                                            boxShadow: "0 20px 25px -5px rgba(99, 102, 241, 0.4), 0 10px 10px -5px rgba(99, 102, 241, 0.04)"
                                        }
                                        : {}
                                }
                                whileTap={
                                    (message.trim() || attachments.length > 0) && !sending
                                        ? { scale: 0.95 }
                                        : {}
                                }
                                transition={{ type: "spring", stiffness: 400, damping: 25 }}
                            >
                                {/* Animated background gradient */}
                                {(message.trim() || attachments.length > 0) && !sending && (
                                    <motion.div
                                        className="absolute inset-0 bg-gradient-to-r from-purple-500 via-indigo-500 to-purple-500"
                                        animate={{
                                            x: ["-100%", "100%"],
                                        }}
                                        transition={{
                                            duration: 3,
                                            repeat: Infinity,
                                            ease: "linear"
                                        }}
                                    />
                                )}
                                <div className="relative z-10 flex items-center">
                                    {sending ? (
                                        <motion.span
                                            className="flex items-center"
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1 }}
                                        >
                                            <motion.svg
                                                className="w-4 h-4 text-white mr-2"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                animate={{ rotate: 360 }}
                                                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                            >
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </motion.svg>
                                            Sending
                                        </motion.span>
                                    ) : (
                                        <motion.span
                                            className="flex items-center"
                                            whileHover={{ x: 2 }}
                                            transition={{ type: "spring", stiffness: 400, damping: 25 }}
                                        >
                                            Send
                                            <motion.svg
                                                className="ml-2 w-4 h-4"
                                                viewBox="0 0 20 20"
                                                fill="currentColor"
                                                whileHover={{ x: 3, rotate: -15 }}
                                                transition={{ type: "spring", stiffness: 400, damping: 25 }}
                                            >
                                                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                                            </motion.svg>
                                        </motion.span>
                                    )}
                                </div>
                            </motion.button>
                        </div>

                        {/* Display attachments preview */}
                        {attachments.length > 0 && (
                            <div className="flex flex-wrap gap-2 mt-2 pt-2 border-t border-gray-200 dark:border-gray-800">
                                {attachments.map((attachment) => (
                                    <div key={attachment.id} className="relative group">
                                        <img
                                            src={attachment.preview}
                                            alt="Attachment preview"
                                            className="h-16 w-16 rounded-md object-cover border border-gray-200 dark:border-gray-800"
                                        />
                                        <button
                                            onClick={() => setAttachments(prev => prev.filter(a => a.id !== attachment.id))}
                                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
                                        >
                                            ×
                                        </button>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Gift Modal */}
            {(() => {
                const partnerInfo = getUserInfo(chat, currentUserId);
                return (
                    <GiftModal
                        isOpen={isGiftModalOpen}
                        onClose={() => {
                            setIsGiftModalOpen(false);
                        }}
                        onSendGift={handleSendGift}
                        conversationId={chat.id}
                        conversationPartnerName={partnerInfo?.nickname || partnerInfo?.name || 'Unknown User'}
                        recipientUserId={partnerInfo?.id}
                    />
                );
            })()}

            {/* Gift Notification */}
            <GiftNotification
                gift={receivedGift?.gift}
                senderName={receivedGift?.sender_name || "Someone"}
                isVisible={showGiftNotification}
                onClose={() => setShowGiftNotification(false)}
                onView={() => {
                    setShowGiftNotification(false);
                    // Scroll to the gift message if possible
                    const giftMessageEl = document.getElementById(`message-${receivedGift?.message_id}`);
                    if (giftMessageEl) {
                        giftMessageEl.scrollIntoView({ behavior: 'smooth' });
                    }
                }}
                position="bottom-right"
            />
        </div>
    );
};

export default React.memo(ChatWindow);
