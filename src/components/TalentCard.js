/******************************************************************
 * TalentCard – ENHANCED GAMING EDITION (UI/UX Enhanced)
 * Props unchanged – swap file and enjoy
 *****************************************************************/
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, useMotionValue, useTransform } from 'framer-motion';
import { FaUserPlus, FaUserCheck, FaBolt, FaFire, FaDiamond } from 'react-icons/fa6';
import { formatTalentForDisplay } from '../models/TalentModel';
import { useToggleFollow } from '../hooks/talent/useTalents';
import { getAvailabilityStatus, getAvailabilityAnimation } from '../utils/availabilityUtils';

const TalentCard = ({ talent }) => {
  const formattedTalent = formatTalentForDisplay(talent);
  const {
    id,
    displayName,
    levelNumber,
    profileImage,
    lowestPrice,
    isFeatured,
    rating,
    services = [],
    verified,
    vip,
    availabilityStatus
  } = formattedTalent;

  const navigate = useNavigate();
  const toggleFollowMutation = useToggleFollow();
  const [isFollowing, setIsFollowing] = useState(formattedTalent.isFollowing);
  const [followLoading, setFollowLoading] = useState(false);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });

  /* Enhanced 3-D hover tilt */
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const rotateX = useTransform(y, [-100, 100], [12, -12]);
  const rotateY = useTransform(x, [-100, 100], [-12, 12]);

  const handleCardClick = () => navigate(`/talents/${id}`);

  const handleFollowClick = async (e) => {
    e.stopPropagation();
    setFollowLoading(true);
    const newState = !isFollowing;
    setIsFollowing(newState);
    try {
      const res = await toggleFollowMutation.mutateAsync({
        talentId: id,
        isFollowing: newState,
      });
      if (typeof res?.is_following === 'boolean')
        setIsFollowing(res.is_following);
    } catch {
      setIsFollowing((prev) => !prev);
    }
    setFollowLoading(false);
  };

  // Get availability status information
  const availabilityInfo = getAvailabilityStatus(availabilityStatus);

  const defaultImageUrl = '/AuthLogo.png';
  const maxServices = 2;
  const visibleServices = services.slice(0, maxServices);
  const extraServices = services.length - maxServices;

  /* Enhanced rarity color helper with more vibrant gradients */
  const rarityColor = (idx) => {
    const colors = [
      'from-cyan-400 via-blue-500 to-purple-600',
      'from-pink-400 via-purple-500 to-indigo-600',
      'from-emerald-400 via-teal-500 to-cyan-600',
      'from-orange-400 via-red-500 to-pink-600'
    ];
    return colors[idx % colors.length];
  };

  return (
    <motion.div
      className="group relative w-full aspect-square rounded-3xl overflow-hidden cursor-pointer"
      style={{ perspective: 1200 }}
      onClick={handleCardClick}
      whileHover={{ scale: 1.03 }}
      onMouseMove={(e) => {
        const rect = e.currentTarget.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        x.set(e.clientX - centerX);
        y.set(e.clientY - centerY);
        setMousePos({
          x: ((e.clientX - rect.left) / rect.width) * 100,
          y: ((e.clientY - rect.top) / rect.height) * 100
        });
      }}
      onMouseLeave={() => {
        x.set(0);
        y.set(0);
        setMousePos({ x: 50, y: 50 });
      }}
    >
      {/* Enhanced Outer Glow */}
      <motion.div
        className="absolute -inset-2 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
        style={{
        //   background: `conic-gradient(from 0deg at 50% 50%, 
        //     #06b6d4 0deg, #8b5cf6 72deg, #ec4899 144deg, 
        //     #f59e0b 216deg, #10b981 288deg, #06b6d4 360deg)`,
          filter: 'blur(20px)',
          animation: 'spin 6s linear infinite'
        }}
      />

      {/* Tilt container */}
      <motion.div
        style={{ rotateX, rotateY, transformStyle: 'preserve-3d' }}
        className="w-full h-full relative"
      >
        {/* Background image with enhanced effects */}
        <div className="absolute inset-0 overflow-hidden rounded-3xl">
          <img
            src={profileImage || defaultImageUrl}
            alt={displayName}
            className="w-full h-full object-cover object-center scale-110 group-hover:scale-125 transition-transform duration-700 brightness-90 group-hover:brightness-110"
            onError={(e) => {
              e.target.src = defaultImageUrl;
            }}
          />
          
          {/* Dynamic spotlight effect */}
          <motion.div
            className="absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-500"
            style={{
              background: `radial-gradient(circle at ${mousePos.x}% ${mousePos.y}%, 
                rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.1) 30%, transparent 60%)`
            }}
          />
        </div>

        {/* Enhanced Vignette + scanlines */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/40 to-transparent" />
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/60" />
        <div
          className="absolute inset-0 opacity-[0.08] mix-blend-overlay"
          style={{
            backgroundImage:
              'repeating-linear-gradient(0deg,transparent,transparent 2px,rgba(0,255,255,0.06) 2px,rgba(0,255,255,0.06) 4px)',
          }}
        />

        {/* Enhanced neon frame with animated gradient */}
        {/* <div className="absolute inset-0 rounded-3xl border-2 border-transparent bg-gradient-to-r from-cyan-400/60 via-purple-500/60 to-pink-500/60 group-hover:from-cyan-400/80 group-hover:via-purple-500/80 group-hover:to-pink-500/80 transition-all duration-300">
          <div className="absolute inset-[2px] rounded-3xl bg-transparent" />
        </div> */}

        {/* Enhanced shadow with multiple colors */}
        <div className="absolute inset-0 rounded-3xl shadow-[0_0_30px_5px_rgba(6,182,212,0.25),0_0_60px_10px_rgba(139,92,246,0.15)] group-hover:shadow-[0_0_40px_8px_rgba(6,182,212,0.4),0_0_80px_15px_rgba(139,92,246,0.25),0_0_120px_20px_rgba(236,72,153,0.15)] transition-all duration-500" />

        {/* Enhanced animated corner lines */}
        {/* <svg
          className="absolute top-0 left-0 w-full h-full pointer-events-none"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
        >
          <motion.path
            d="M 3 3 L 25 3 L 3 25"
            stroke="url(#enhancedGrad1)"
            strokeWidth="0.8"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 1.5, ease: 'easeInOut' }}
          />
          <motion.path
            d="M 97 3 L 75 3 L 97 25"
            stroke="url(#enhancedGrad2)"
            strokeWidth="0.8"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 1.5, ease: 'easeInOut', delay: 0.2 }}
          />
          <motion.path
            d="M 3 97 L 25 97 L 3 75"
            stroke="url(#enhancedGrad3)"
            strokeWidth="0.8"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 1.5, ease: 'easeInOut', delay: 0.4 }}
          />
          <motion.path
            d="M 97 97 L 75 97 L 97 75"
            stroke="url(#enhancedGrad4)"
            strokeWidth="0.8"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 1.5, ease: 'easeInOut', delay: 0.6 }}
          />
          <defs>
            <linearGradient id="enhancedGrad1" x1="0" y1="0" x2="1" y2="1">
              <stop offset="0%" stopColor="#22d3ee" />
              <stop offset="50%" stopColor="#a855f7" />
              <stop offset="100%" stopColor="#ec4899" />
            </linearGradient>
            <linearGradient id="enhancedGrad2" x1="0" y1="0" x2="1" y2="1">
              <stop offset="0%" stopColor="#a855f7" />
              <stop offset="50%" stopColor="#ec4899" />
              <stop offset="100%" stopColor="#f59e0b" />
            </linearGradient>
            <linearGradient id="enhancedGrad3" x1="0" y1="0" x2="1" y2="1">
              <stop offset="0%" stopColor="#ec4899" />
              <stop offset="50%" stopColor="#f59e0b" />
              <stop offset="100%" stopColor="#10b981" />
            </linearGradient>
            <linearGradient id="enhancedGrad4" x1="0" y1="0" x2="1" y2="1">
              <stop offset="0%" stopColor="#f59e0b" />
              <stop offset="50%" stopColor="#10b981" />
              <stop offset="100%" stopColor="#22d3ee" />
            </linearGradient>
          </defs>
        </svg> */}

        {/* Top Status Bar - NEW: Availability Status */}
        <div className="absolute top-4 left-4 right-4 flex justify-between items-start z-20">
          {/* Availability Status Indicator */}
          <motion.div
            className="flex items-center gap-2 px-3 py-2 rounded-xl backdrop-blur-md border border-white/20"
            style={{
              background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'
            }}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="relative">
              <div className={`w-3 h-3 ${availabilityInfo.bgColor} rounded-full ${getAvailabilityAnimation(availabilityInfo.color)}`} />
              {availabilityInfo.color === 'green' && (
                <div className={`absolute inset-0 w-3 h-3 ${availabilityInfo.bgColor} rounded-full animate-ping opacity-60`} />
              )}
            </div>
            <span className="text-white text-xs font-bold tracking-wider drop-shadow-lg">
              {availabilityInfo.label.toUpperCase()}
            </span>
          </motion.div>

          {/* Level Badge - Enhanced */}
          <motion.div
            className="flex items-center gap-1 px-3 py-2 rounded-xl backdrop-blur-md border border-white/20"
            style={{
              background: 'linear-gradient(135deg, rgba(139,92,246,0.3) 0%, rgba(59,130,246,0.2) 100%)'
            }}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <FaBolt className="w-3 h-3 text-cyan-300" />
            <span className="text-white text-xs font-black tracking-wider">
              LVL {levelNumber}
            </span>
          </motion.div>
        </div>

        {/* Content overlay - Enhanced */}
        <div className="absolute bottom-0 left-0 w-full p-5 text-white">
          {/* Enhanced Featured Ribbon */}
          {isFeatured && (
            <motion.div 
              className="absolute -top-6 -right-6 flex items-center gap-1 px-4 py-2 text-xs font-black uppercase tracking-widest rounded-2xl shadow-2xl"
              style={{
                background: 'linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ff6b9d 100%)',
                boxShadow: '0 0 20px rgba(255,107,107,0.6)'
              }}
              initial={{ scale: 0, rotate: -45 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ type: "spring", delay: 0.5 }}
            >
              <FaFire className="w-3 h-3 animate-pulse" />
              <span>FEATURED</span>
              <FaDiamond className="w-3 h-3 animate-bounce" />
            </motion.div>
          )}

          {/* Enhanced Name & Badges */}
          <motion.div 
            className="flex items-center gap-3 mb-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <h3 className="text-2xl font-black truncate">
              <span className="bg-gradient-to-r from-cyan-300 via-purple-300 to-pink-300 bg-clip-text text-transparent drop-shadow-lg">
                {displayName}
              </span>
            </h3>
            
            {/* Enhanced Verified Badge */}
            {verified && (
              <motion.div 
                className="w-7 h-7 rounded-full flex items-center justify-center shadow-lg"
                style={{
                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                  boxShadow: '0 0 15px rgba(59,130,246,0.5)'
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </motion.div>
            )}
            
            {/* Enhanced VIP Badge */}
            {vip && (
              <motion.div 
                className="w-7 h-7 rounded-full flex items-center justify-center shadow-lg"
                style={{
                  background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #ec4899 100%)',
                  boxShadow: '0 0 15px rgba(251,191,36,0.5)'
                }}
                whileHover={{ scale: 1.1, rotate: 10 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </motion.div>
            )}
          </motion.div>

          {/* Enhanced Rating */}
          <motion.div 
            className="flex items-center gap-2 mb-3"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.7 }}
          >
            <div className="flex items-center gap-1 px-3 py-1 rounded-lg backdrop-blur-sm" style={{
              background: 'linear-gradient(135deg, rgba(251,191,36,0.2) 0%, rgba(245,158,11,0.3) 100%)',
              border: '1px solid rgba(251,191,36,0.3)'
            }}>
              <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              <span className="text-white font-bold text-sm drop-shadow">
                {typeof rating === 'number' ? rating.toFixed(1) : '5.0'}
              </span>
            </div>
          </motion.div>

          {/* Enhanced Services */}
          <motion.div 
            className="flex flex-wrap gap-2 mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            {visibleServices.map((s, idx) => (
              <motion.span
                key={idx}
                className={`px-3 py-1.5 text-xs font-black rounded-lg bg-gradient-to-r ${rarityColor(idx)} text-white shadow-lg`}
                style={{
                  boxShadow: '0 4px 15px rgba(0,0,0,0.3), 0 0 10px rgba(255,255,255,0.1)'
                }}
                whileHover={{ scale: 1.05, y: -2 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.9 + idx * 0.1 }}
              >
                {typeof s === 'string' ? s : s.type || s.name}
              </motion.span>
            ))}
            {extraServices > 0 && (
              <motion.span 
                className="px-3 py-1.5 text-xs font-black rounded-lg text-white shadow-lg"
                style={{
                  background: 'linear-gradient(135deg, rgba(75,85,99,0.8) 0%, rgba(55,65,81,0.9) 100%)',
                  border: '1px solid rgba(156,163,175,0.3)'
                }}
                whileHover={{ scale: 1.05, y: -2 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.1 }}
              >
                +{extraServices} more
              </motion.span>
            )}
          </motion.div>

          {/* Enhanced Bottom row */}
          <motion.div 
            className="flex items-center justify-between"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2 }}
          >
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-white/80">From</span>
              <span className="text-lg font-black bg-gradient-to-r from-cyan-300 to-emerald-300 bg-clip-text text-transparent drop-shadow">
                <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-5 h-5 mb-2 inline-block" />
                +++ Credits
              </span>
            </div>

            <motion.button
              whileHover={{ scale: 1.15, rotate: 5 }}
              whileTap={{ scale: 0.85 }}
              onClick={handleFollowClick}
              disabled={followLoading}
              className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 shadow-lg ${
                isFollowing
                  ? 'bg-gradient-to-r from-emerald-400 to-teal-500 text-white shadow-emerald-400/40'
                  : 'bg-gradient-to-r from-white/20 to-white/10 text-white backdrop-blur-sm hover:from-white/30 hover:to-white/20 border border-white/20'
              }`}
              style={{
                boxShadow: isFollowing 
                  ? '0 0 20px rgba(52,211,153,0.4), 0 4px 15px rgba(0,0,0,0.3)' 
                  : '0 4px 15px rgba(0,0,0,0.3), 0 0 10px rgba(255,255,255,0.1)'
              }}
            >
              {followLoading ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : isFollowing ? (
                <FaUserCheck className="w-4 h-4" />
              ) : (
                <FaUserPlus className="w-4 h-4" />
              )}
            </motion.button>
          </motion.div>
        </div>

        {/* Enhanced holographic overlay */}
        <motion.div
          className="absolute inset-0 pointer-events-none rounded-3xl opacity-0 group-hover:opacity-20 transition-opacity duration-500"
          style={{
            background: `
              conic-gradient(from ${mousePos.x}deg at ${mousePos.x}% ${mousePos.y}%, 
              transparent 0deg, rgba(255,255,255,0.1) 90deg, transparent 180deg, 
              rgba(0,255,255,0.1) 270deg, transparent 360deg)
            `
          }}
        />
      </motion.div>

      {/* CSS Animation for outer glow */}
      <style>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </motion.div>
  );
};

export default TalentCard;