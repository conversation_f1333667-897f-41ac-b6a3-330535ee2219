import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTalentFullProfile, useToggleFollow } from '../hooks/talent/useTalents';
import { useAuth } from '../contexts/AuthContext';
import { SectionLoader } from '../components/ui/LoadingIndicator';
import CoverImage from './profile/CoverImage';
import ProfileCard from './profile/ProfileCard';
import OrderConfirmationPage from './profile/OrderConfirmationPage';
import MainNavigation from './navigation/MainNavigation';
import MobileNavigation from './navigation/MobileNavigation';
import { motion, AnimatePresence } from 'framer-motion';
import ServiceSelectionModal from './profile/ServiceSelectionModal';
import UserAvailabilityModal from './profile/UserAvailabilityModal';
import SkillsTab from './profile/SkillsTab';
import PostTab from './profile/PostTab';
import PostView from './PostView';
import ServiceSelectionPortal from './profile/ServiceSelectionPortal';
import { FaUsers, FaTrophy, FaEnvelope, FaUserPlus, FaPlay, FaPause, FaVolumeUp, FaGift, FaShoppingCart } from 'react-icons/fa';
import { getCdnUrl } from '../utils/cdnUtils';
import ReviewTab from './profile/ReviewTab';
import MissionTab from './profile/MissionTab';
// Add import for useState for modal state
import { Dialog, DialogContent, DialogTitle, DialogDescription } from './ui/dialog';

// Step-by-step order guide modal
const OrderGuideModal = ({ isOpen, onClose, skills, onComplete }) => {
  const [step, setStep] = useState(1);
  const [selectedSkill, setSelectedSkill] = useState(null);

  if (!isOpen) return null;
  return (
    <Dialog open={isOpen} onOpenChange={v => { if (!v) onClose(); }}>
      <DialogContent className="max-w-2xl w-full p-0 bg-transparent shadow-none border-none">
        <div className="relative bg-white dark:bg-gray-900 rounded-2xl shadow-2xl max-w-2xl w-full mx-auto p-6 z-10">
          <div className="flex items-center justify-between mb-4">
            <DialogTitle asChild>
              <h2 className="text-xl font-bold text-indigo-700 dark:text-yellow-400" id="order-guide-title">How to Order a Service</h2>
            </DialogTitle>
            <button onClick={onClose} className="p-2 rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700">
              <svg className="w-5 h-5 text-gray-700 dark:text-gray-200" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /></svg>
            </button>
          </div>
          <div className="mb-6 flex items-center gap-2">
            {[1,2,3,4,5,6].map(n => (
              <div key={n} className={`w-6 h-2 rounded-full transition-all duration-200 ${step === n ? 'bg-indigo-500 dark:bg-yellow-400' : 'bg-gray-200 dark:bg-gray-700'}`}></div>
            ))}
          </div>
          <DialogDescription asChild>
            <div id="order-guide-desc" className="sr-only">
              {step === 1 && 'Step 1: Select a service type from the options below.'}
              {step === 2 && 'Step 2: Pick a package/tier for the selected service.'}
              {step === 3 && 'Step 3: Choose when you want the service (Order Now or Schedule for Later).'}
              {step === 4 && 'Step 4: Set quantity, add remarks, and review price.'}
              {step === 5 && 'Step 5: Review your selections and place your order.'}
              {step === 6 && 'Final Step: Scroll down and press the Message Talent button to start a conversation.'}
            </div>
          </DialogDescription>
          {step === 1 && (
            <div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-yellow-400" id="order-guide-step1">Step 1: Select a Service</h3>
              <p className="mb-4 text-gray-600 dark:text-gray-300">Choose a service type from the options below. This is what you want to order from the talent.</p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                {skills.map(skill => (
                  <div
                    key={skill.id}
                    className={`rounded-xl border-2 transition-all duration-200 p-4 cursor-pointer bg-white/80 dark:bg-gray-900/80 shadow hover:shadow-lg ${selectedSkill?.id === skill.id ? 'border-indigo-500 dark:border-yellow-400 ring-2 ring-indigo-400 dark:ring-yellow-400' : 'border-gray-200 dark:border-gray-700'}`}
                    onClick={() => setSelectedSkill(skill)}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <img src={getCdnUrl(skill.image || skill.icon || '/public/missionx-logo.png')} alt={skill.title || skill.name} className="w-10 h-10 rounded-lg object-cover bg-gray-200" />
                      <div>
                        <div className="font-bold text-indigo-700 dark:text-yellow-400">{skill.title || skill.name}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-300">{skill.service_category?.name || ''}</div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-700 dark:text-gray-200 mb-1 line-clamp-2">{skill.description}</div>
                    {skill.rates && skill.rates.length > 0 && (
                      <div className="text-xs text-indigo-600 dark:text-yellow-400 font-semibold">From {skill.rates[0].price} {skill.rates[0].unit}</div>
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-end gap-2">
                <button onClick={onClose} className="px-4 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 font-semibold">Cancel</button>
                <button
                  onClick={() => setStep(2)}
                  disabled={!selectedSkill}
                  className={`px-6 py-2 rounded-lg font-bold transition-all duration-200 ${selectedSkill ? 'bg-indigo-600 text-white hover:bg-indigo-700 dark:bg-yellow-400 dark:text-gray-900 dark:hover:bg-yellow-300' : 'bg-gray-200 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed'}`}
                >
                  Next
                </button>
              </div>
            </div>
          )}
          {step === 2 && (
            <div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-yellow-400">Step 2: Select a Tier</h3>
              <p className="mb-4 text-gray-600 dark:text-gray-300">Pick a package/tier for the selected service. (Mocked UI)</p>
              <div className="rounded-xl border-2 border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-900/80 p-4 mb-4">
                <div className="font-bold text-indigo-700 dark:text-yellow-400 mb-2">{selectedSkill?.title || selectedSkill?.name}</div>
                <div className="flex flex-col gap-2">
                  {(selectedSkill?.rates || []).map((rate, idx) => (
                    <div key={idx} className="flex items-center justify-between p-2 rounded-lg bg-indigo-50 dark:bg-gray-800">
                      <div>
                        <div className="font-semibold text-indigo-700 dark:text-yellow-400">{rate.type}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-300">{rate.description}</div>
                      </div>
                      <div className="font-bold text-indigo-700 dark:text-yellow-400">{rate.price} {rate.unit}</div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex justify-between gap-2">
                <button onClick={() => setStep(1)} className="px-4 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 font-semibold">Back</button>
                <button onClick={() => setStep(3)} className="px-6 py-2 rounded-lg bg-indigo-600 text-white font-bold hover:bg-indigo-700 dark:bg-yellow-400 dark:text-gray-900 dark:hover:bg-yellow-300 transition-all duration-200">Next</button>
              </div>
            </div>
          )}
          {step === 3 && (
            <div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-yellow-400">Step 3: Scheduling</h3>
              <p className="mb-4 text-gray-600 dark:text-gray-300">Choose when you want the service (Order Now or Schedule for Later). (Mocked UI)</p>
              <div className="flex gap-4 mb-4">
                <div className="flex-1 p-4 rounded-xl border-2 border-indigo-200 dark:border-yellow-400 bg-white/80 dark:bg-gray-900/80 text-center cursor-pointer">Order Now</div>
                <div className="flex-1 p-4 rounded-xl border-2 border-indigo-200 dark:border-yellow-400 bg-white/80 dark:bg-gray-900/80 text-center cursor-pointer">Schedule for Later</div>
              </div>
              <div className="flex justify-between gap-2">
                <button onClick={() => setStep(2)} className="px-4 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 font-semibold">Back</button>
                <button onClick={() => setStep(4)} className="px-6 py-2 rounded-lg bg-indigo-600 text-white font-bold hover:bg-indigo-700 dark:bg-yellow-400 dark:text-gray-900 dark:hover:bg-yellow-300 transition-all duration-200">Next</button>
              </div>
            </div>
          )}
          {step === 4 && (
            <div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-yellow-400">Step 4: Configure Order</h3>
              <p className="mb-4 text-gray-600 dark:text-gray-300">Set quantity, add remarks, and review price. (Mocked UI)</p>
              <div className="rounded-xl border-2 border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-900/80 p-4 mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-semibold text-indigo-700 dark:text-yellow-400">Quantity</span>
                  <span className="font-bold text-indigo-700 dark:text-yellow-400">1</span>
                </div>
                <div className="mb-2">
                  <span className="font-semibold text-indigo-700 dark:text-yellow-400">Remarks</span>
                  <div className="text-xs text-gray-500 dark:text-gray-300">(Optional)</div>
                  <div className="mt-1 p-2 rounded bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200">No remarks</div>
                </div>
                <div className="flex items-center justify-between mt-2">
                  <span className="font-semibold text-indigo-700 dark:text-yellow-400">Total</span>
                  <span className="font-bold text-indigo-700 dark:text-yellow-400">{selectedSkill?.rates?.[0]?.price || 0}</span>
                </div>
              </div>
              <div className="flex justify-between gap-2">
                <button onClick={() => setStep(3)} className="px-4 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 font-semibold">Back</button>
                <button onClick={() => setStep(5)} className="px-6 py-2 rounded-lg bg-indigo-600 text-white font-bold hover:bg-indigo-700 dark:bg-yellow-400 dark:text-gray-900 dark:hover:bg-yellow-300 transition-all duration-200">Next</button>
              </div>
            </div>
          )}
          {step === 5 && (
            <div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-yellow-400">Step 5: Confirm & Place Order</h3>
              <p className="mb-4 text-gray-600 dark:text-gray-300">Review your selections and place your order. (Mocked UI)</p>
              <div className="rounded-xl border-2 border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-900/80 p-4 mb-4">
                <div className="font-bold text-indigo-700 dark:text-yellow-400 mb-2">{selectedSkill?.title || selectedSkill?.name}</div>
                <div className="text-xs text-gray-500 dark:text-gray-300 mb-2">Tier: {selectedSkill?.rates?.[0]?.type || 'N/A'}</div>
                <div className="text-xs text-gray-500 dark:text-gray-300 mb-2">Quantity: 1</div>
                <div className="text-xs text-gray-500 dark:text-gray-300 mb-2">Total: {selectedSkill?.rates?.[0]?.price || 0}</div>
              </div>
              <div className="flex justify-between gap-2">
                <button onClick={() => setStep(4)} className="px-4 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 font-semibold">Back</button>
                <button onClick={() => setStep(6)} className="px-6 py-2 rounded-lg bg-green-600 text-white font-bold hover:bg-green-700 transition-all duration-200">Next</button>
              </div>
            </div>
          )}
          {step === 6 && (
            <div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-yellow-400">Final Step: Message Talent</h3>
              <p className="mb-4 text-gray-600 dark:text-gray-300">After placing your order, scroll down to the bottom of the confirmation step and press the <span className='font-bold text-indigo-700 dark:text-yellow-400'>'Message Talent'</span> button. This will open the chat page and send a friendly message to the talent to start your conversation.</p>
              <div className="rounded-xl border-2 border-indigo-200 dark:border-yellow-400 bg-white/80 dark:bg-gray-900/80 p-4 mb-4">
                <div className="font-bold text-indigo-700 dark:text-yellow-400 mb-2">Message Preview</div>
                <div className="text-sm text-gray-700 dark:text-gray-200">
                  Hi! 🌟 I just booked your {selectedSkill?.title || selectedSkill?.name} service (x1) and I'm really looking forward to collaborating with you! Can't wait to see what we create together.
                </div>
              </div>
              <div className="flex justify-between gap-2">
                <button onClick={() => setStep(5)} className="px-4 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 font-semibold">Back</button>
                <button onClick={() => { onComplete && onComplete(); onClose(); }} className="px-6 py-2 rounded-lg bg-green-600 text-white font-bold hover:bg-green-700 transition-all duration-200">Finish Guide</button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

const ProfileCardOverlay = ({ profileData, isFollowing, onFollow, isAuthenticated, followLoading, onOrder, handleMessage, isOwnProfile }) => {
  // Use camelCase consistently for all fields
  const profilePic = profileData?.profileImage || '/public/missionx-logo.png';
  const name = profileData?.displayName || 'Unknown Talent';
  const handle = profileData?.username || profileData?.handle || '';
  const followers = profileData?.followers ?? 0;
  const bio = profileData?.bio || '';
  const missionsDone = profileData?.missionsCompleted ?? 0;
  const rating = profileData?.rating ?? 0;
  const reviewCount = profileData?.reviewCount ?? 0;
  const age = profileData?.age ?? '--';
  const constellation = profileData?.constellation || 'Zodiac';
  const height = profileData?.height ?? '--';
  const weight = profileData?.weight ?? '--';
  // Stat cards as in the reference image
  const statCards = [
    {
      icon: '🧑‍🤝‍🧑',
      value: followers,
      label: 'Followers',
      iconBg: 'from-blue-100 to-indigo-100',
    },
    {
      icon: '🎯',
      value: missionsDone,
      label: 'Mission Done',
      iconBg: 'from-orange-100 to-pink-100',
    },
    {
      icon: '⭐',
      value: rating,
      label: `Reviews (${reviewCount})`,
      iconBg: 'from-yellow-100 to-orange-100',
    },
    {
      icon: '🌙',
      value: `${age} Years Old`,
      label: constellation,
      iconBg: 'from-purple-100 to-pink-100',
    },
    {
      icon: '🧳',
      value: `${height} cm`,
      label: `${weight} kg`,
      iconBg: 'from-green-100 to-blue-100',
    },
  ];
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = React.useRef(null);
  const voiceNoteUrl = profileData?.voiceNoteUrl;

  // Handle play/pause
  const handlePlayPause = () => {
    if (!audioRef.current) return;
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
  };
  // Sync play state
  React.useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;
    const handleEnded = () => setIsPlaying(false);
    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    return () => {
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
    };
  }, []);

  return (
    <motion.div
      className="w-full max-w-7xl mx-auto bg-white/80 dark:bg-gray-900/80 backdrop-blur-2xl border border-indigo-100 rounded-3xl shadow-2xl flex flex-col md:flex-row items-stretch gap-2 md:gap-8 px-2 sm:px-4 md:px-8 py-4 sm:py-6 md:py-8 relative overflow-hidden group transition-all duration-300"
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.7, ease: 'easeOut' }}
    >
      {/* Animated gradient blob decorations */}
      <div className="absolute -top-12 -right-16 w-40 h-40 bg-gradient-to-br from-blue-400/30 to-indigo-400/30 rounded-full blur-2xl animate-pulse z-0" />
      <div className="absolute -bottom-16 -left-16 w-32 h-32 bg-gradient-to-br from-pink-400/30 to-blue-400/30 rounded-full blur-xl animate-pulse delay-1000 z-0" />
      {/* Glow border on hover */}
      <div className="absolute inset-0 rounded-3xl pointer-events-none group-hover:ring-2 group-hover:ring-blue-400/60 transition-all duration-300" />
      {/* Profile Picture - always top left */}
      <div className="flex-shrink-0 flex flex-col items-start justify-start z-10 w-full md:w-auto md:mr-6 relative group/profilepic mb-4 md:mb-0">
        {/* Animated gradient ring */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-36 h-36 md:w-44 md:h-44 rounded-full bg-gradient-to-tr from-indigo-400 via-blue-400 to-pink-400 animate-spin-slow opacity-70 blur-sm group-hover/profilepic:scale-105 transition-transform duration-300" style={{ zIndex: 1 }} />
        </div>
        <img
          src={profilePic}
          alt={name}
          className="relative w-40 h-40 md:w-40 md:h-40 rounded-full border-4 border-white dark:border-gray-900 shadow-2xl object-cover object-center bg-gray-200 group-hover/profilepic:scale-105 transition-transform duration-300"
          style={{ zIndex: 2 }}
        />
      </div>
      {/* Main Info and Actions */}
      <div className="flex-1 flex flex-col md:flex-row items-center md:items-start gap-4 md:gap-6 w-full z-10">
        {/* Main Info with glassmorphism overlay */}
        <div className="flex-1 flex flex-col items-center md:items-start text-center md:text-left gap-2 md:gap-6 w-full">
          <div className="w-full rounded-xl bg-white/30 dark:bg-gray-900/40 backdrop-blur-md px-2 sm:px-4 py-2 sm:py-4 shadow-lg">
            {/* UID above name, left-aligned, glassy pill with icon */}
            {profileData?.uid || profileData?.id ? (
              <div className="mb-2 flex justify-start w-full">
                <span className="inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-white/10 to-indigo-400/10 dark:from-yellow-400/10 dark:to-pink-400/10 backdrop-blur-md border border-white/20 rounded-full shadow-md text-sm text-gray-700 dark:text-yellow-400 tracking-widest font-mono transition hover:bg-indigo-500/20">
                  <svg className="w-4 h-4 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 20 20">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5.121 17.804A13.937 13.937 0 0110 16c1.657 0 3.24.267 4.879.804M15 10a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  UID: {profileData.uid}
                </span>
              </div>
            ) : null}
            <div className="flex flex-wrap items-center justify-center md:justify-start w-full gap-1 sm:gap-2">
              <span className="text-lg sm:text-xl md:text-3xl font-extrabold bg-gradient-to-r from-blue-700 to-indigo-400 bg-clip-text text-transparent dark:from-yellow-400 dark:to-pink-400 drop-shadow-[0_2px_8px_rgba(0,0,0,0.25)] uppercase">
                {name}
              </span>
              {handle && (
                <span className="text-xs sm:text-sm md:text-lg text-gray-500 dark:text-gray-300 font-mono ml-1 md:ml-2">@{handle}</span>
              )}
              {/* Status badge beside name/handle */}
              {(() => {
                const statusHex = profileData?.availabilityStatus || profileData?.availability_status || '';
                let label = 'Offline';
                let color = 'bg-gray-400';
                if (statusHex === '#00FF00') {
                  label = 'Online, I am ready to chat'; color = 'bg-green-400';
                } else if (statusHex === '#FFFF00') {
                  label = 'Busy'; color = 'bg-yellow-400';
                } else if (statusHex === '#FF0000') {
                  label = 'Offline'; color = 'bg-red-500';
                }
                return (
                  <>
                    <span className={`flex items-center text-xs md:text-sm font-medium px-1.5 py-0.5 rounded-full ml-1 md:ml-2 bg-gray-100 dark:bg-gray-800/80 shadow border border-gray-200 dark:border-gray-700`}>
                      <span className={`w-3 h-3 md:w-4 md:h-4 rounded-full mr-1 ${color} inline-block`} />
                      {label}
                    </span>
                    {/* Voice note play button - OUTSIDE status badge */}
                    {voiceNoteUrl && (
                      <span className="ml-2 md:ml-3 flex items-center relative">
                        {/* Animated SVG waveform around play button */}
                        <span className="absolute inset-0 flex items-center justify-center pointer-events-none z-0">
                          <svg
                            width="44"
                            height="44"
                            viewBox="0 0 44 44"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className={`transition-opacity duration-300 ${isPlaying ? 'opacity-100' : 'opacity-40'}`}
                          >
                            {/* 8 animated bars in a circle */}
                            {[...Array(8)].map((_, i) => {
                              const angle = (i * 45) * (Math.PI / 180);
                              const r1 = 16, r2 = 20;
                              const x1 = 22 + r1 * Math.cos(angle);
                              const y1 = 22 + r1 * Math.sin(angle);
                              const x2 = 22 + r2 * Math.cos(angle);
                              const y2 = 22 + r2 * Math.sin(angle);
                              return (
                                <line
                                  key={i}
                                  x1={x1}
                                  y1={y1}
                                  x2={x2}
                                  y2={y2}
                                  stroke="#6366f1"
                                  strokeWidth="3"
                                  strokeLinecap="round"
                                  style={{
                                    opacity: 0.7,
                                    transformOrigin: '22px 22px',
                                    animation: isPlaying ? `wave-bar 1s ${(i * 0.1)}s infinite ease-in-out` : 'none',
                                  }}
                                  className="wave-bar-svg"
                                />
                              );
                            })}
                          </svg>
                        </span>
                        <button
                          onClick={handlePlayPause}
                          className={`relative w-9 h-9 flex items-center justify-center rounded-full bg-gradient-to-tr from-indigo-400 via-blue-400 to-pink-400 shadow-lg border-2 border-white dark:border-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-400 transition-all duration-200 ${isPlaying ? 'ring-2 ring-blue-400 animate-pulse' : ''}`}
                          title={isPlaying ? 'Pause Voice Message' : 'Play Voice Message'}
                          aria-label={isPlaying ? 'Pause Voice Message' : 'Play Voice Message'}
                          type="button"
                          style={{ zIndex: 1 }}
                        >
                          <span className="absolute inset-0 rounded-full bg-white/30 dark:bg-gray-900/30 backdrop-blur-md" />
                          {isPlaying ? (
                            <FaPause className="w-5 h-5 text-indigo-700 relative z-10" />
                          ) : (
                            <FaPlay className="w-5 h-5 text-indigo-700 relative z-10" />
                          )}
                        </button>
                        <audio ref={audioRef} src={voiceNoteUrl} preload="auto" />
                      </span>
                    )}
                  </>
                );
              })()}
            </div>
            {/* Bio/About */}
            {bio && (
              <div className="mt-1 text-gray-700 text-justify dark:text-gray-300 text-xs sm:text-sm md:text-base max-w-3xl">
                {bio}
              </div>
            )}
          </div>
          {/* Stat Cards Row with glassmorphism and vibrant gradients */}
          <div className="flex flex-nowrap md:flex-wrap overflow-x-auto justify-center md:justify-start gap-2 md:gap-4 mt-2 md:mt-3 w-full pb-2 md:pb-0">
            {statCards.map((stat, idx) => (
              <div
                key={idx}
                className="flex flex-col items-center justify-center bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border border-indigo-100 dark:border-gray-800 rounded-2xl shadow-lg px-2 sm:px-3 py-2 min-w-[100px] max-w-[130px] w-[100px] sm:w-full relative overflow-hidden"
                style={{ minHeight: 60 }}
              >
                {/* Animated blob for stat card */}
                <div className="absolute -top-6 -right-6 w-12 h-12 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-xl animate-pulse z-0" />
                <span className={`w-13 h-13 flex items-center justify-center rounded-full mb-1 text-2xl bg-gradient-to-br ${stat.iconBg}`}>
                  {stat.icon}
                </span>
                <span className="text-sm sm:text-base font-bold text-gray-900 dark:text-white z-10">{stat.value}</span>
                <span className="text-xs sm:text-sm text-gray-500 dark:text-gray-300 text-center font-medium mt-0.5 z-10">
                  {stat.label}
                </span>
              </div>
            ))}
          </div>
        </div>
        {/* Action Buttons */}
        <div className="flex flex-col gap-2 md:gap-4 items-center md:items-start mt-2 md:mt-0 z-10 w-full md:w-auto">
          <button
            className="w-full sm:w-36 px-4 sm:px-6 py-2 sm:py-3 rounded-full font-bold shadow-xl transition-all duration-200 flex items-center justify-center gap-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 border-2 border-indigo-600 bg-indigo-600 text-white hover:bg-indigo-700 hover:border-indigo-700 active:scale-95 dark:bg-indigo-500 dark:border-indigo-400 dark:text-white dark:hover:bg-indigo-600 dark:hover:border-indigo-500"
            onClick={handleMessage}
            type="button"
            disabled={isOwnProfile}
            aria-disabled={isOwnProfile}
            title={isOwnProfile ? 'You cannot message yourself' : 'Message'}
          >
            <FaEnvelope /> Message
          </button>
          <button
            className={`w-full sm:w-36 px-4 sm:px-6 py-2 sm:py-3 rounded-full font-bold shadow-xl transition-all duration-200 flex items-center justify-center gap-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 border-2 ${isFollowing ? 'bg-white text-indigo-700 border-indigo-300 hover:bg-indigo-50 dark:bg-gray-900 dark:text-indigo-200 dark:border-indigo-500 dark:hover:bg-gray-800' : 'bg-indigo-100 text-indigo-700 border-indigo-300 hover:bg-indigo-200 dark:bg-indigo-900 dark:text-indigo-200 dark:border-indigo-500 dark:hover:bg-indigo-800'} active:scale-95`}
            onClick={() => onFollow(!isFollowing)}
            disabled={!isAuthenticated || followLoading || isOwnProfile}
            aria-disabled={!isAuthenticated || followLoading || isOwnProfile}
            title={isOwnProfile ? 'You cannot follow yourself' : (isFollowing ? 'Unfollow' : 'Follow')}
          >
            <FaUserPlus />
            {followLoading ? (
              <span>...</span>
            ) : isFollowing ? 'Unfollow' : 'Follow'}
          </button>
          <button
            className="w-full sm:w-36 px-4 sm:px-6 py-2 sm:py-3 rounded-full font-bold shadow-xl transition-all duration-200 flex items-center justify-center gap-2 mt-1 focus:outline-none focus:ring-2 focus:ring-pink-500 border-2 border-pink-600 bg-pink-600 text-white hover:bg-pink-700 hover:border-pink-700 active:scale-95 dark:bg-pink-500 dark:border-pink-400 dark:text-white dark:hover:bg-pink-600 dark:hover:border-pink-500"
            onClick={() => {}}
            disabled
            aria-disabled="true"
            title="Gifting feature coming soon!"
          >
            <FaGift /> Gift Me (Coming Soon)
          </button>
          <button
            className="w-full sm:w-36 px-4 sm:px-6 py-2 sm:py-3 rounded-full font-bold shadow-xl transition-all duration-200 flex items-center justify-center gap-2 mt-1 focus:outline-none focus:ring-2 focus:ring-indigo-500 border-2 border-indigo-600 bg-indigo-600 text-white hover:bg-indigo-700 hover:border-indigo-700 active:scale-95 dark:bg-indigo-500 dark:border-indigo-400 dark:text-white dark:hover:bg-indigo-600 dark:hover:border-indigo-500"
            onClick={onOrder}
            disabled={isOwnProfile}
            aria-disabled={isOwnProfile}
            title={isOwnProfile ? 'You cannot order from yourself' : 'Order Guide'}
          >
            <FaShoppingCart /> Order Guide
          </button>
        </div>
      </div>
    </motion.div>
  );
};

const TalentProfile = () => {
    const { talentId } = useParams();
    const { isAuthenticated, user } = useAuth();
    // Remove activeTab and setActiveTab
    const [placedOrder, setPlacedOrder] = useState(null);
    const [showOrderConfirmation, setShowOrderConfirmation] = useState(false);
    const [coverMedia, setCoverMedia] = useState([]);
    const [processedTalent, setProcessedTalent] = useState(null);
    const navigate = useNavigate();
    // State for UserAvailabilityModal
    const [showAvailabilityModal, setShowAvailabilityModal] = useState(false);
    // Follow button loading state (must be at top level)
    const [followLoading, setFollowLoading] = useState(false);
    // Service selection modal state
    const [showServiceModal, setShowServiceModal] = useState(false);
    const [selectedSkill, setSelectedSkill] = useState(null);
    // Add state for the guide modal
    const [showOrderGuide, setShowOrderGuide] = useState(false);
    // Add state for PostView modal
    const [showPostView, setShowPostView] = useState(false);
    const [viewingPostId, setViewingPostId] = useState(null);

    const handleOpenServiceModal = (skill) => {
        if (!processedTalent || !Array.isArray(processedTalent.availabilityData) || processedTalent.availabilityData.length === 0) {
            console.warn('Cannot open modal: availabilityData not loaded or empty.', processedTalent?.availabilityData);
            return;
        }
        console.log('Opening modal with availabilityData:', processedTalent.availabilityData);
        setSelectedSkill(skill);
        setShowServiceModal(true);
    };

    const handleCloseServiceModal = () => {
        setShowServiceModal(false);
        setSelectedSkill(null);
    };

    const handleOrderPlaced = () => {
        setShowServiceModal(false);
        setSelectedSkill(null);
    };

    // Use React Query for talent data fetching
    const {
        data: talent,
        isLoading,
        error,
        refetch
    } = useTalentFullProfile(talentId, {
        retry: 3,
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    });

    // Use React Query for follow/unfollow functionality
    const toggleFollowMutation = useToggleFollow();

    // Utility to process backend media into [{type, url, order}] for CoverImage
    const getCoverMediaArray = (profileMedia) => {
      if (!profileMedia) return [];

      const media = [];

      const addMedia = (item) => {
        if (item && item.url) {
          media.push(item);
        }
      };

      // Handle video definitions
      if (profileMedia.video) {
        if (Array.isArray(profileMedia.video)) {
          profileMedia.video.forEach((video, idx) => {
            const url = getCdnUrl(video.url || video.path || video);
            const thumbnail = getCdnUrl(
              video.thumbnail || profileMedia.thumbnail || ''
            );
            addMedia({
              type: 'video',
              url,
              thumbnail,
              order: video.order || idx + 1,
            });
          });
        } else if (typeof profileMedia.video === 'object') {
          const url = getCdnUrl(
            profileMedia.video.url || profileMedia.video.path || ''
          );
          const thumbnail = getCdnUrl(
            profileMedia.video.thumbnail || profileMedia.thumbnail || ''
          );
          addMedia({
            type: 'video',
            url,
            thumbnail,
            order: profileMedia.video.order || 1,
          });
        } else if (typeof profileMedia.video === 'string') {
          addMedia({
            type: 'video',
            url: getCdnUrl(profileMedia.video),
            thumbnail: getCdnUrl(profileMedia.thumbnail || ''),
            order: 1,
          });
        }
      }

      // Handle photos
      if (Array.isArray(profileMedia.photos)) {
        profileMedia.photos.forEach((photo, idx) => {
          addMedia({
            type: 'image',
            url: getCdnUrl(photo.path || photo.url || photo),
            order: photo.order || idx + 1,
          });
        });
      }

      if (media.length === 0) {
        media.push({ type: 'image', url: '', order: 1 });
      }

      media.sort((a, b) => (a.order || 0) - (b.order || 0));

      return media;
    };

    // Process talent data to extract cover media and voice note
    const processTalentData = (talentData) => {
        if (!talentData) return null;
        // Convert all snake_case fields to camelCase for consistency
        const toCamel = (str) => str.replace(/_([a-z])/g, (g) => g[1].toUpperCase());
        const camelObj = (obj) => {
          if (Array.isArray(obj)) return obj.map(camelObj);
          if (obj && typeof obj === 'object') {
            return Object.keys(obj).reduce((acc, key) => {
              acc[toCamel(key)] = camelObj(obj[key]);
              return acc;
            }, {});
          }
          return obj;
        };
        const camelTalent = camelObj(talentData);
        // Use the original 'profile_media' field if present
        const profileMedia =
            talentData.profile_media ||
            camelTalent.profileMedia ||
            camelTalent.mediaFiles ||
            camelTalent.media ||
            {};
        const coverMedia = getCoverMediaArray(profileMedia);
        // Process voice note
        let voiceNoteUrl = null;
        if (camelTalent.voiceNote) {
            voiceNoteUrl = camelTalent.voiceNote.startsWith('http')
                ? camelTalent.voiceNote
                : `${process.env.REACT_APP_CDN_URL}/${camelTalent.voiceNote}`;
        }
        // Extract availability info robustly
        let availabilityData = [];
        let availabilityRemarks = null;
        let availabilityIsAvailable = null;
        if (Array.isArray(camelTalent.userAvailabilities) && camelTalent.userAvailabilities.length > 0) {
          // Try to find the first object with a non-empty availabilityData or availability_data
          const found = camelTalent.userAvailabilities.find(av =>
            (Array.isArray(av.availabilityData) && av.availabilityData.length > 0) ||
            (Array.isArray(av.availability_data) && av.availability_data.length > 0)
          ) || camelTalent.userAvailabilities[0];
          availabilityData = found.availabilityData || found.availability_data || [];
          availabilityRemarks = found.remarks || found.remark || null;
          availabilityIsAvailable = typeof found.isAvailable === 'boolean' ? found.isAvailable : (typeof found.is_available === 'boolean' ? found.is_available : null);
        }
        // Extract firstAvailability as before
        const firstAvailability = Array.isArray(talentData.user_availabilities)
            ? talentData.user_availabilities[0]
            : null;
        // Do NOT remap or flatten services/skills, preserve original backend objects
        // Pass through the original services/skills array as 'skills'
        return {
            ...talentData,
            coverMedia,
            voiceNoteUrl,
            availabilityData: firstAvailability?.availability_data || [],
            availabilityRemarks: firstAvailability?.remarks || null,
            availabilityIsAvailable:
                typeof firstAvailability?.is_available === 'boolean'
                    ? firstAvailability.is_available
                    : null,
            availabilityStatus:
                talentData.availabilityStatus ??
                talentData.availability_status ??
                null,
            // Preserve original backend services/skills array
            skills: Array.isArray(talentData.skills) && talentData.skills.length > 0
                ? talentData.skills
                : (Array.isArray(talentData.services) && talentData.services.length > 0
                    ? talentData.services
                    : []),
        };
    };

    // Process talent data when it changes
    useEffect(() => {
        if (talent) {
            const processed = processTalentData(talent);
            setProcessedTalent(processed);
            console.log('Processed Talent availabilityData:', processed?.availabilityData);
        }
    }, [talent]);

    // Handlers
    const handleTabChange = (tab) => {
        // This function is no longer needed as tabs are removed
    };
    
    const handleFollow = async (nextIsFollowing) => {
        try {
            const res = await toggleFollowMutation.mutateAsync({ talentId, isFollowing: nextIsFollowing });
            if (typeof res?.is_following === 'boolean') {
                setProcessedTalent(prev => {
                    if (!prev) return prev;
                    // Only update followers count if the state actually changes
                    const wasFollowing = !!prev.isFollowing;
                    const nowFollowing = !!res.is_following;
                    let followers = prev.followers;
                    if (!wasFollowing && nowFollowing) followers += 1;
                    if (wasFollowing && !nowFollowing) followers -= 1;
                    return {
                        ...prev,
                        isFollowing: nowFollowing,
                        followers
                    };
                });
                // Refetch the profile to get the latest backend state
                refetch();
            }
        } catch (err) {
            console.error('Error toggling follow:', err);
        }
    };

    // Always define isFollowing and handleFollowButton at top level
    const isFollowing = !!processedTalent?.isFollowing;
    const handleFollowButton = async (nextIsFollowing) => {
        if (!isAuthenticated || followLoading) return;
        setFollowLoading(true);
        await handleFollow(nextIsFollowing);
        setFollowLoading(false);
    };

    const handleMessage = () => {
        navigate(`/chat?talent_id=${talentId}&action=new_conversation`);
    };

    // Open the service selection modal using the first available skill
    const handleOrderFromProfile = () => {
        if (processedTalent?.skills && processedTalent.skills.length > 0) {
            handleOpenServiceModal(processedTalent.skills[0]);
        }
    };
    
    const handleOrder = (skillOrOrder) => {
        if (skillOrOrder && skillOrOrder.id && skillOrOrder.price) {
            setPlacedOrder(skillOrOrder);
            setShowOrderConfirmation(true);
            return;
        }
        // This function is no longer needed as tabs are removed
    };

    const handleViewOrders = () => {
        navigate('/profile?orders=true');
    };
    
    const handleViewOrder = (order) => navigate(`/orders/${order.id}`);
    
    const handleBackToProfile = () => {
        setShowOrderConfirmation(false);
        setPlacedOrder(null);
    };

    // Handle post viewing from PostTab
    const handleViewPost = (postId) => {
        setViewingPostId(postId);
        setShowPostView(true);
    };

    const handleClosePostView = () => {
        setShowPostView(false);
        setViewingPostId(null);
    };

    // Determine if viewing own profile
    const isOwnProfile = user && processedTalent && (user.id === processedTalent.id);

    if (isLoading || !processedTalent) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-950">
                <SectionLoader type="morph" size="large" message="Loading talent profile..." color="indigo" />
            </div>
        );
    }

    if (error || !talent) {
        const errorMessage = error?.response?.status === 404 
            ? 'Talent profile not found. The talent may have been removed or is no longer available.'
            : error?.response?.status === 401
            ? 'Please log in to view this talent profile.'
            : error?.response?.status === 403
            ? 'You do not have permission to view this talent profile.'
            : error?.message || 'An unexpected error occurred. Please try again later.';

        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-50">
                <div className="text-center p-8 bg-white rounded-2xl shadow-lg max-w-md mx-4">
                    <div className="mb-4">
                        <svg className="w-16 h-16 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h2 className="text-xl font-bold text-gray-900 mb-2">Profile Not Found</h2>
                    <p className="text-gray-600 mb-6">{errorMessage}</p>
                    <div className="flex flex-col sm:flex-row gap-3">
                        <button
                            onClick={() => refetch()}
                            className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-500 text-white rounded-lg hover:from-indigo-700 hover:to-blue-600 transition-all shadow-md hover:shadow-lg transform hover:scale-105 active:scale-100"
                        >
                            Try Again
                        </button>
                    <button
                        onClick={() => navigate('/talent')}
                            className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all shadow-md hover:shadow-lg transform hover:scale-105 active:scale-100"
                    >
                        Back to Talents
                    </button>
                    </div>
                </div>
            </div>
        );
    }

    if (showOrderConfirmation && placedOrder) {
        return (
            <OrderConfirmationPage
                order={placedOrder}
                talent={processedTalent}
                onViewOrder={handleViewOrder}
                onBackToProfile={handleBackToProfile}
            />
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-pink-50 to-blue-50 dark:from-gray-950 dark:to-gray-900 dark:bg-gray-950">
            {/* Availability Overlay */}
            <div className="fixed top-24 left-1/2 transform -translate-x-1/2 z-50">
              <button
                className="px-6 py-2 rounded-full bg-white/80 dark:bg-gray-900/80 shadow-lg border border-indigo-100 dark:border-gray-800 backdrop-blur-md text-indigo-700 dark:text-indigo-200 font-semibold flex items-center gap-2 hover:bg-white/90 hover:scale-105 active:scale-95 transition-all duration-200"
                onClick={() => setShowAvailabilityModal(true)}
                type="button"
              >
                <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2a4 4 0 018 0v2m-4-4V7a4 4 0 10-8 0v6a4 4 0 008 0z" />
                </svg>
                View My Availability
              </button>
            </div>
            <UserAvailabilityModal
              isOpen={showAvailabilityModal}
              onClose={() => setShowAvailabilityModal(false)}
              availabilityData={processedTalent?.availabilityData}
              availabilityRemarks={processedTalent?.availabilityRemarks}
            />
            {/* MainNavigation at the top */}
            <MainNavigation activeItem="/talent" />
            {/* CoverImage for cover media and overlay content */}
            <CoverImage 
                coverMedia={processedTalent?.coverMedia || []} 
                alt={processedTalent?.displayName}
                className="h-[80vh]"
            >
                {/* You can add overlay children here if needed */}
            </CoverImage>
            {/* Main content container with profile card overlay and main content grid */}
            <div className="relative w-full px-2 sm:px-4">
                {/* Profile card overlay, centered and overlapping the hero */}
                <div className="-mt-40 sm:-mt-80 mb-4 sm:mb-8 max-w-7xl mx-auto">
                    <ProfileCardOverlay
                        profileData={processedTalent}
                        isFollowing={isFollowing}
                        onFollow={handleFollowButton}
                        isAuthenticated={isAuthenticated}
                        followLoading={followLoading}
                        onOrder={() => setShowOrderGuide(true)}
                        handleMessage={handleMessage}
                        isOwnProfile={isOwnProfile}
                    />
                </div>
                {/* 2-column layout below profile card overlay, same width as overlay */}
                <div className="max-w-7xl mx-auto">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8">
                      {/* Left column: SkillsTab (2/3 width on desktop) */}
                      <div className="md:col-span-2 flex flex-col gap-4 md:gap-8">
                          <SkillsTab
                              talent={processedTalent}
                              onOpenServiceModal={handleOpenServiceModal}
                              isOwnProfile={isOwnProfile}
                          />
                          <PostTab talent={processedTalent} isOwnProfile={isOwnProfile} onViewPost={handleViewPost} />
                          <MissionTab talent={processedTalent} />
                      </div>
                      {/* Right column: placeholder (1/3 width on desktop) */}
                  <div className="md:col-span-1 mt-4 md:mt-0">
                    <ReviewTab talent={processedTalent} />
                  </div>
              </div>
            </div>
            </div>
            {/* ServiceSelectionModal rendered at the root level using a portal */}
            <ServiceSelectionPortal isOpen={showServiceModal} onClose={handleCloseServiceModal}>
                <ServiceSelectionModal
                key={JSON.stringify(processedTalent?.availabilityData)}
                    isOpen={showServiceModal}
                    onClose={handleCloseServiceModal}
                    skill={selectedSkill}
                    onOrderPlaced={handleOrderPlaced}
                    onViewOrders={handleViewOrders}
                    availabilityData={processedTalent?.availabilityData}
                />
            </ServiceSelectionPortal>
            {/* OrderGuideModal rendered at the root level */}
            <OrderGuideModal
                isOpen={showOrderGuide}
                onClose={() => setShowOrderGuide(false)}
                skills={processedTalent?.skills || []}
                onComplete={() => setShowOrderGuide(false)}
            />
            {/* PostView modal rendered at the root level using a portal */}
            {showPostView && viewingPostId && (
                <div className="fixed inset-0 z-[100] bg-black/50 backdrop-blur-sm">
                    <PostView
                        postId={viewingPostId}
                        isOpen={showPostView}
                        onClose={handleClosePostView}
                    />
                </div>
            )}
        </div>
    );
};

export default TalentProfile;
